<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resources - Khuddam <PERSON>l <PERSON> | Islamic Learning Materials</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Access our collection of Islamic learning resources including Quranic Dictionary app, Lisan-ul-Quran book, and educational materials for Arabic grammar students.">
    <meta name="keywords" content="Quranic Dictionary, Lisan ul Quran, Islamic Resources, Arabic Grammar, Quran Learning, Islamic Books, Mobile Apps">
    <meta name="author" content="Khuddam Ul Quran">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://khuddam.co.nz/resources/">
    <meta property="og:title" content="Resources - Khuddam Ul Quran | Islamic Learning Materials">
    <meta property="og:description" content="Access our collection of Islamic learning resources including Quranic Dictionary app, Lisan-ul-Quran book, and educational materials for Arabic grammar students.">
    <meta property="og:image" content="https://khuddam.co.nz/images/quranic-dictionary.webp">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://khuddam.co.nz/resources/">
    <meta name="twitter:title" content="Resources - Khuddam Ul Quran | Islamic Learning Materials">
    <meta name="twitter:description" content="Access our collection of Islamic learning resources including Quranic Dictionary app, Lisan-ul-Quran book, and educational materials for Arabic grammar students.">
    <meta name="twitter:image" content="https://khuddam.co.nz/images/quranic-dictionary.webp">

    <!-- Critical CSS loaded directly for faster rendering -->
    <link rel="stylesheet" href="css/critical.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- External CSS resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Structured Data -->
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@graph": [
                {
                    "@type": "WebPage",
                    "@id": "https://khuddam.co.nz/resources/",
                    "url": "https://khuddam.co.nz/resources/",
                    "name": "Resources - Khuddam Ul Quran",
                    "description": "Access our collection of Islamic learning resources including Quranic Dictionary app, Lisan-ul-Quran book, and educational materials for Arabic grammar students.",
                    "isPartOf": {
                        "@id": "https://khuddam.co.nz/#website"
                    },
                    "about": {
                        "@id": "https://khuddam.co.nz/#organization"
                    },
                    "mainEntity": [
                        {
                            "@type": "SoftwareApplication",
                            "name": "Quranic Dictionary",
                            "description": "Digital Quranic dictionary for Arabic grammar students to search meaning of Quranic words quickly",
                            "applicationCategory": "EducationalApplication",
                            "operatingSystem": ["iOS", "Android"],
                            "author": {
                                "@type": "Organization",
                                "name": "Khuddam-ul-Quran NZ"
                            }
                        },
                        {
                            "@type": "Book",
                            "name": "Lisan-Ul Quran English Version",
                            "description": "English adaptation of Lisān ul-Qur'an by Ustādh Aamir Sohail for broader accessibility",
                            "author": {
                                "@type": "Person",
                                "name": "Ustādh Aamir Sohail"
                            },
                            "inLanguage": "en"
                        }
                    ]
                }
            ]
        }
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="images/old-logo-border.png">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="images/quranic-dictionary.webp" as="image">
    <link rel="preload" href="images/lisanulquran-english-part1.webp" as="image">
</head>

<body class="font-sans bg-light text-gray-800 overflow-x-hidden">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 bg-dark flex items-center justify-center z-50">
        <div class="text-center">
            <div class="mb-4">
                <img src="images/old-logo-border.png" alt="Khuddam Logo" class="h-16 mx-auto animate-pulse">
            </div>
            <div class="text-primary text-xl font-medium mb-4">السلام عليكم ورحمة الله وبركاته</div>
            <div class="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
    </div>

    <div id="main-content">
    <!-- Header with Navigation -->
    <header class="bg-dark/95 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="/">
                        <img src="images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="/about/" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="/news/" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="/events/" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="/resources/" class="text-primary font-medium hover:text-white transition">Resources</a>
                    <a href="/contact_form/" class="text-white font-medium hover:text-primary transition">Contact</a>
                    <a href="/registration_form/" class="border border-primary px-6 py-2 text-white font-medium hover:text-primary transition flex items-center justify-center">Register Now</a>
                </div>

                <!-- Mobile Navigation Toggle -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-white hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </nav>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden fixed inset-0 bg-dark bg-opacity-95 z-50">
                <!-- Close button -->
                <button id="mobile-menu-close" class="absolute top-6 right-6 text-white hover:text-primary">
                    <i class="fas fa-times text-2xl"></i>
                </button>

                <!-- Menu content -->
                <div class="flex flex-col items-center justify-center h-full w-full">
                    <div class="flex flex-col items-center space-y-10 py-8">
                        <a href="/" class="text-white text-2xl font-medium hover:text-primary transition">Home</a>
                        <a href="/about/" class="text-white text-2xl font-medium hover:text-primary transition">About Us</a>
                        <a href="/news/" class="text-white text-2xl font-medium hover:text-primary transition">News</a>
                        <a href="/events/" class="text-white text-2xl font-medium hover:text-primary transition">Events</a>
                        <a href="/resources/" class="text-primary text-2xl font-medium hover:text-white transition">Resources</a>
                        <a href="/contact_form/" class="text-white text-2xl font-medium hover:text-primary transition">Contact</a>
                        <a href="/registration_form/" class="text-white text-2xl font-medium hover:text-primary transition">Register Now</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title Section -->
    <section class="relative pt-32 pb-20 bg-dark">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-4">Resources</h1>
                <div class="w-32 h-1 bg-primary mx-auto mb-8"></div>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    Explore our collection of Islamic learning resources, apps, and educational materials
                </p>
            </div>
        </div>
    </section>

    <!-- Quranic Dictionary App Section -->
    <section class="relative overflow-hidden py-20 bg-white">
        <div class="container mx-auto px-6 relative z-10">
            <!-- Section Title -->
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4 text-gray-800">Quranic Dictionary App</h2>
                <div class="w-24 h-1 bg-primary mx-auto"></div>
            </div>

            <!-- App Content -->
            <div class="flex flex-col lg:flex-row gap-12 max-w-6xl mx-auto items-center">
                <!-- Left Side - App Image -->
                <div class="lg:w-1/2 order-2 lg:order-1">
                    <div class="relative">
                        <img src="images/andriod.webp" alt="Quranic Dictionary App Screenshot" class="w-full max-w mx-auto">
                    </div>
                </div>

                <!-- Right Side - App Description and Download Buttons -->
                <div class="lg:w-1/2 order-1 lg:order-2">
                    <div class="space-y-6">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">About this app</h3>
                        <p class="text-gray-600 leading-relaxed">
                            This Quranic dictionary is written by Lutfur Rahman Sb (late) and published by Al Balagh Foundation Lahore, Pakistan.
                        </p>
                        <p class="text-gray-600 leading-relaxed">
                            For the sake of Allah SWT, Khuddam-ul-Quran NZ has attempted to digitize it for the benefits of all Arabic grammar students to search meaning of Quranic words quickly. Alhamdulillah.
                        </p>
                        <p class="text-gray-600 leading-relaxed">
                            This dictionary only contains Root letters containing 3 letters and 4 letters which are used in Quran.
                        </p>
                        <p class="text-gray-600 leading-relaxed">
                            For further clarification of word's meaning, relevant Quranic verse is also mentioned.
                        </p>
                        <p class="text-gray-600 leading-relaxed font-medium">
                            May Allah SWT accept our efforts. Ameen
                        </p>

                        <!-- Download Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4 pt-6">
                            <a href="https://apps.apple.com/nz/app/digital-quranic-dictionary/id1638033757" class="inline-flex items-center justify-center px-6 py-3 bg-black text-white rounded-lg hover:bg-primary transition-colors duration-300 font-medium">
                                <i class="fab fa-apple mr-3 text-xl"></i>
                                Download for iOS
                            </a>
                            <a href="https://play.google.com/store/apps/details?id=com.digital.quranicdictionaryy" class="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-green-600 transition-colors duration-300 font-medium">
                                <i class="fab fa-google-play mr-3 text-xl"></i>
                                Download for Android
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lisan-Ul Quran Book Section -->
    <section class="relative overflow-hidden py-20 bg-gray-50">
        <div class="container mx-auto px-6 relative z-10">
            <!-- Section Title -->
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4 text-gray-800">Lisan-Ul Quran Book English Version</h2>
                <div class="w-24 h-1 bg-primary mx-auto"></div>
            </div>

            <!-- Book Content -->
            <div class="flex flex-col lg:flex-row gap-12 max-w-6xl mx-auto items-center">
                <!-- Left Side - Book Image -->
                <div class="lg:w-1/2 order-2 lg:order-1">
                    <div class="relative">
                        <img src="images/lisanulquran-english-part1.webp" alt="Lisan-Ul Quran English Version Book Cover" class="w-full max-w-md mx-auto shadow-2xl rounded-lg">
                    </div>
                </div>

                <!-- Right Side - Book Description -->
                <div class="lg:w-1/2 order-1 lg:order-2">
                    <div class="space-y-6">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">About The Book</h3>
                        <p class="text-gray-600 leading-relaxed">
                            This book is inspired by and derived from Lisān ul-Qur'an (لِسَانُ القُرْآنِ) by Ustādh Aamir Sohail. The original structure and methodology belong to the authors and publishers of Lisān ul-Qur'an, who hold exclusive copyright to the source material.
                        </p>
                        <p class="text-gray-600 leading-relaxed">
                            This work is an adaptation designed for broader accessibility, and no copyright infringement is intended.
                        </p>
                    </div>
                </div>
            </div>

            <!-- PDF Viewer Section -->
            <div class="mt-16 max-w-6xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Read Online</h3>
                    <div class="bg-gray-100 rounded-lg p-8 text-center">
                        <div class="space-y-4">
                            <i class="fas fa-file-pdf text-6xl text-red-500"></i>
                            <h4 class="text-xl font-semibold text-gray-700">PDF Viewer</h4>
                            <p class="text-gray-600">
                                PDF file will be embedded here for direct reading access.
                            </p>
                            <p class="text-sm text-gray-500">
                                Once the PDF file is uploaded, readers will be able to view and navigate through the book directly on this page.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="relative py-20 bg-dark">
        <div class="container mx-auto px-4 text-center">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Enhance Your Islamic Learning Journey
                </h2>
                <p class="text-xl text-white/80 mb-8 leading-relaxed">
                    Join our community and access more resources to deepen your understanding of the Quran and Arabic language
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="/registration_form/" class="inline-flex items-center justify-center px-8 py-4 bg-primary text-dark font-semibold hover:bg-primary/90 transition-all duration-300 transform hover:scale-105">
                        <span class="flex items-center">
                            <i class="fas fa-user-plus mr-2"></i>
                            Join Our Classes
                        </span>
                    </a>
                    <a href="/contact_form/" class="inline-flex items-center justify-center px-8 py-4 border-2 border-primary text-primary hover:bg-primary hover:text-dark transition-all duration-300 transform hover:scale-105">
                        <span class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Us
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark/95 shadow-md text-white -mt-1">
        <div class="container mx-auto px-4 py-10">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Logos -->
                <div class="mb-6 md:mb-0 flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="mr-2">
                            <img src="images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12">
                        </div>
                    </div>
                    <div class="flex items-center">
                        <a href="/">
                            <img src="images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12">
                        </a>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="/about/" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="/news/" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="/events/" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="/resources/" class="text-primary font-medium hover:text-white transition">Resources</a>
                    <a href="/registration_form/" class="text-white font-medium hover:text-primary transition">Register Now</a>
                    <a href="/contact_form/" class="text-white font-medium hover:text-primary transition">Contact</a>
                </div>

                <!-- Social Media -->
                <div class="flex space-x-4">
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://www.youtube.com/@KhuddamUlQuranNZ" target="_blank" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-white/10 mt-8 pt-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
                    <div class="mt-4 md:mt-0">
                        <ul class="flex space-x-6 text-sm">
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Developer Credits -->
                <div class="mt-4 pt-4 border-t border-white/5">
                    <div class="text-center">
                        <p class="text-white/50 text-xs">
                            Website developed by
                            <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                                Kiwiorbit
                            </a>
                            | Contact:
                            <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                                +64 22 325 9094
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js" defer></script>
    </div><!-- End of main-content -->

    <!-- Loader Script -->
    <script src="js/loader.js" defer></script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    </script>

</body>
</html>
