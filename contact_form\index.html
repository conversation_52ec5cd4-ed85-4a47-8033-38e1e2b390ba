<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Khuddam - Servants of the Quran | Islamic Education</title>
    <meta name="description" content="Contact us about our Quranic Arabic Grammar Learning Programme. Enrollment open for male adults starting February/March 2025.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://khuddam.co.nz/contact-form.html">
    <meta property="og:title" content="Contact Khuddam - Enrollment Open for Quranic Arabic Classes">
    <meta property="og:description" content="Have questions about our Quranic Arabic Grammar Learning Programme? Contact us today. Free classes for male adults starting February/March 2025.">
    <meta property="og:image" content="images/enrollment-post.png">
    <meta property="og:image:alt" content="Khuddam Quranic Arabic Grammar Learning Programme">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://khuddam.co.nz/contact-form.html">
    <meta name="twitter:title" content="Contact Khuddam - Enrollment Open for Quranic Arabic Classes">
    <meta name="twitter:description" content="Have questions about our Quranic Arabic Grammar Learning Programme? Contact us today. Free classes for male adults starting February/March 2025.">
    <meta name="twitter:image" content="images/enrollment-post.png">
    <meta name="twitter:image:alt" content="Khuddam Quranic Arabic Grammar Learning Programme">

    <!-- Critical CSS inlined for faster rendering -->
    <style>
/* Critical CSS for above-the-fold content */

/* Basic body styling */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #101010;
    margin: 0;
    padding: 0;
}

/* Arabic text styling */
.font-arabic, .arabic-text {
    font-family: 'Amiri', serif;
    direction: rtl;
}

/* Main content transition */
#main-content {
    transition: opacity 1s ease, visibility 1s ease;
}

/* Header styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    transition: all 0.3s;
    background-color: rgba(16, 16, 16, 0.95);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile menu styles */
#mobile-menu {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

#mobile-menu.hidden {
    display: none;
}

#mobile-menu:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

/* Color variables */
:root {
    --primary: #deae35;
    --dark: #101010;
}

/* Utility classes used in above-the-fold content */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.text-primary {
    color: #deae35;
}

.text-white {
    color: #ffffff;
}

.bg-dark\/95 {
    background-color: rgba(16, 16, 16, 0.95);
}

.border-primary {
    border-color: #deae35;
}

.shadow-lg {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .md\:hidden {
        display: none;
    }

    .md\:flex {
        display: flex;
    }
}

@media (max-width: 767px) {
    .hidden.md\:flex {
        display: none;
    }
}
    </style>

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Preload critical icons -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

    <!-- Google Fonts - full set loaded asynchronously -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap"></noscript>

    <!-- Custom CSS loaded asynchronously -->
    <link rel="stylesheet" href="css/critical.css">

    <!-- Helper function for CSS loading -->
    <script>
        /* Fallback for browsers that don't support preload */
        !function(n){"use strict";n.loadCSS||(n.loadCSS=function(){});var t=loadCSS.relpreload={};if(t.support=function(){var e;try{e=n.document.createElement("link").relList.supports("preload")}catch(t){e=!1}return function(){return e}}(),t.bindMediaToggle=function(t){var e=t.media||"all";function a(){t.addEventListener?t.removeEventListener("load",a):t.attachEvent&&t.detachEvent("onload",a),t.setAttribute("onload",null),t.media=e}t.addEventListener?t.addEventListener("load",a):t.attachEvent&&t.attachEvent("onload",a),setTimeout(function(){t.rel="stylesheet",t.media="only x"}),setTimeout(a,3e3)},t.poly=function(){if(!t.support())for(var e=n.document.getElementsByTagName("link"),a=0;a<e.length;a++){var o=e[a];"preload"!==o.rel||"style"!==o.getAttribute("as")||o.getAttribute("data-loadcss")||(o.setAttribute("data-loadcss",!0),t.bindMediaToggle(o))}},!t.support()){t.poly();var e=n.setInterval(t.poly,500);n.addEventListener?n.addEventListener("load",function(){t.poly(),n.clearInterval(e)}):n.attachEvent&&n.attachEvent("onload",function(){t.poly(),n.clearInterval(e)})}"undefined"!=typeof exports?exports.loadCSS=loadCSS:n.loadCSS=loadCSS}("undefined"!=typeof global?global:this);
    </script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebPage",
                "@id": "https://khuddam.co.nz/contact-form.html#webpage",
                "url": "https://khuddam.co.nz/contact-form.html",
                "name": "Contact Khuddam - Servants of the Quran | Islamic Education",
                "description": "Contact us about our Quranic Arabic Grammar Learning Programme. Enrollment open for male adults starting February/March 2025.",
                "isPartOf": {
                    "@id": "https://khuddam.co.nz/#website"
                },
                "inLanguage": "en",
                "breadcrumb": {
                    "@id": "https://khuddam.co.nz/contact-form.html#breadcrumblist"
                },
                "about": {
                    "@id": "https://khuddam.co.nz/#organization"
                }
            },
            {
                "@type": "BreadcrumbList",
                "@id": "https://khuddam.co.nz/contact-form.html#breadcrumblist",
                "itemListElement": [
                    {
                        "@type": "ListItem",
                        "position": 1,
                        "item": {
                            "@id": "https://khuddam.co.nz/",
                            "name": "Home"
                        }
                    },
                    {
                        "@type": "ListItem",
                        "position": 2,
                        "item": {
                            "@id": "https://khuddam.co.nz/contact-form.html",
                            "name": "Contact"
                        }
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://khuddam.co.nz/#organization",
                "name": "Khuddam Ul Quran",
                "url": "https://khuddam.co.nz/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://khuddam.co.nz/images/old-logo-border.png",
                    "width": 300,
                    "height": 100,
                    "caption": "Khuddam - Servants of the Quran"
                },
                "contactPoint": {
                    "@type": "ContactPoint",
                    "telephone": "+64 22 161 5574",
                    "contactType": "customer service",
                    "email": "<EMAIL>",
                    "availableLanguage": ["English", "Arabic"],
                    "hoursAvailable": [
                        {
                            "@type": "OpeningHoursSpecification",
                            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                            "opens": "18:00",
                            "closes": "22:00"
                        },
                        {
                            "@type": "OpeningHoursSpecification",
                            "dayOfWeek": ["Sunday"],
                            "opens": "06:00",
                            "closes": "08:00"
                        }
                    ]
                },
                "address": {
                    "@type": "PostalAddress",
                    "streetAddress": "60 Stoddard Road",
                    "addressLocality": "Mount Roskill",
                    "addressRegion": "Auckland",
                    "postalCode": "1041",
                    "addressCountry": "NZ"
                },
                "hasMap": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3190.3613137734387!2d174.7252605877332!3d-36.90562470058159!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6d0d468a422423bb%3A0xa8c01a1fc5e3779e!2s60%20Stoddard%20Road%2C%20Mount%20Roskill%2C%20Auckland%201041!5e0!3m2!1sen!2snz!4v1747623465439!5m2!1sen!2snz"
            },
            {
                "@type": "FAQPage",
                "@id": "https://khuddam.co.nz/contact-form.html#faqpage",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "What is the Quranic Arabic Grammar Learning Programme?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Our Quranic Arabic Grammar Learning Programme is a structured course designed to help adult male students understand the grammatical structure of the Arabic language as used in the Quran. The programme focuses on making complex grammar concepts accessible through a unique teaching methodology that connects directly to Quranic verses."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Do I need prior knowledge of Arabic to join?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "No prior knowledge of Arabic is required to join our programme. We start from the basics and gradually build up your understanding. Our teaching approach is designed to accommodate beginners while still being valuable for those with some prior exposure to the language."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "How long does the programme last?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "The complete programme typically runs for 12 months, with classes held once a week. Each session lasts for one hour. The programme is divided into three levels: beginner, intermediate, and advanced. Students progress through these levels at their own pace, with assessments at the end of each level."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Is there any fee for the programme?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "No, our programme is completely free of cost. We believe in making Quranic education accessible to all. However, registration is required as we have limited seats available for each batch. Students are expected to commit to regular attendance and participation."
                        }
                    }
                ]
            }
        ]
    }
    </script>

    <style>
        .map-container {
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            height: 100%;
            position: relative;
        }
        .contact-card {
            border-left: 4px solid transparent;
        }
        .form-input {
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
        }
        .form-input:focus {
            border-color: #deae35;
            box-shadow: 0 0 0 2px rgba(222, 174, 53, 0.2);
        }
    </style>
</head>
<body class="bg-black font-sans text-dark">
    <div id="main-content" style="opacity: 1; visibility: visible;">
    <!-- Header with Navigation -->
    <header class="bg-dark/95 shadow-lg fixed top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="/">
                        <img src="images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="news.html" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="resources.html" class="text-white font-medium hover:text-primary transition">Resources</a>
                    <a href="contact-form.html" class="text-primary font-medium hover:text-white transition">Contact</a>
                    <a href="registration-form.html" class="border border-primary px-6 py-2 text-white font-medium hover:text-primary transition flex items-center justify-center">Register Now</a>
                </div>

                <!-- Mobile Navigation Toggle -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-white hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </nav>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden fixed inset-0 bg-dark bg-opacity-95 z-50">
                <!-- Close button -->
                <button id="mobile-menu-close" class="absolute top-6 right-6 text-white hover:text-primary">
                    <i class="fas fa-times text-2xl"></i>
                </button>

                <!-- Menu content -->
                <div class="flex flex-col items-center justify-center h-full w-full">
                    <div class="flex flex-col items-center space-y-10 py-8">
                        <a href="/" class="text-white text-2xl font-medium hover:text-primary transition">Home</a>
                        <a href="about.html" class="text-white text-2xl font-medium hover:text-primary transition">About Us</a>
                        <a href="news.html" class="text-white text-2xl font-medium hover:text-primary transition">News</a>
                        <a href="events.html" class="text-white text-2xl font-medium hover:text-primary transition">Events</a>
                        <a href="resources.html" class="text-white text-2xl font-medium hover:text-primary transition">Resources</a>
                        <a href="contact-form.html" class="text-primary text-2xl font-medium hover:text-white transition">Contact</a>
                        <a href="registration-form.html" class="text-white text-2xl font-medium hover:text-primary transition">Register Now</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title Section -->
    <section class="relative pt-32 pb-20 bg-dark">
        <div class="absolute inset-0 bg-gradient-to-b from-dark/80 to-dark/95"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-4">Contact Us</h1>
                <div class="w-32 h-1 bg-primary mx-auto mb-8"></div>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    Have questions about our Quranic Arabic Grammar Learning Programme? We're here to help.
                </p>
                <div class="flex justify-center mt-8">
                    <div class="inline-flex items-center px-4 py-2 bg-white/10 rounded-sm">
                        <i class="fas fa-clock text-primary mr-2"></i>
                        <span class="text-white/90">Response Time: Within 24-48 Hours</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="py-20 bg-white -mt-1 relative overflow-hidden">
        <!-- Background -->
        <div class="absolute inset-0 w-full h-full">
            <!-- Foldpages Background Image -->
            <img src="./images/foldpagesquran.webp" alt="" class="absolute inset-0 w-full h-full object-cover opacity-20">
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-5xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <!-- Contact Info 1 -->
                    <div class="bg-white/80 border border-primary p-6 shadow-lg contact-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary/10 flex items-center justify-center mr-4 contact-icon">
                                <i class="fas fa-map-marker-alt text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold">Our Location</h3>
                        </div>
                        <p class="text-gray-600">60 Stoddard Road, Mount Roskill, Auckland, New Zealand</p>
                    </div>

                    <!-- Contact Info 2 -->
                    <div class="bg-white/80 border border-primary p-6 shadow-lg contact-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary/10 flex items-center justify-center mr-4 contact-icon">
                                <i class="fas fa-envelope text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold">Email Us</h3>
                        </div>
                        <p class="text-gray-600"><EMAIL></p>
                    </div>

                    <!-- Contact Info 3 -->
                    <div class="bg-white/80 border border-primary p-6 shadow-lg contact-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary/10 flex items-center justify-center mr-4 contact-icon">
                                <i class="fas fa-phone-alt text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold">Call Us</h3>
                        </div>
                        <p class="text-gray-600">+64 22 161 5574</p>
                        <p class="text-gray-600">Mon-Fri, 6pm-10pm</p>
                    </div>
                </div>

                <!-- Map and Contact Form Two-Column Layout -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Map Section -->
                    <div class="bg-white/80 border border-primary shadow-lg border border-gray-200 overflow-hidden h-full">
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">Find Us</h2>
                            <div class="map-container" style="height: 350px;">
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3190.3613137734387!2d174.7252605877332!3d-36.90562470058159!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6d0d468a422423bb%3A0xa8c01a1fc5e3779e!2s60%20Stoddard%20Road%2C%20Mount%20Roskill%2C%20Auckland%201041!5e0!3m2!1sen!2snz!4v1747623465439!5m2!1sen!2snz" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>

                            <div class="mt-6 pt-6">
                                <h3 class="text-lg font-bold text-gray-800 mb-3">Operating Hours</h3>
                                <ul class="space-y-2">
                                    <li class="flex justify-between">
                                        <span class="text-gray-600">Monday - Friday:</span>
                                        <span class="text-gray-800 font-medium">6:00 PM - 10:00 PM</span>
                                    </li>
                                    <li class="flex justify-between">
                                        <span class="text-gray-600">Saturday:</span>
                                        <span class="text-gray-800 font-medium">Closed</span>
                                    </li>
                                    <li class="flex justify-between">
                                        <span class="text-gray-600">Sunday:</span>
                                        <span class="text-gray-800 font-medium">6:00 AM - 8:00 AM</span>
                                    </li>
                                    <li class="text-center mt-4 pt-2 border-t border-gray-200">
                                        <span class="text-gray-600 text-sm">Sunday classes at</span>
                                        <br>
                                        <span class="text-gray-800 font-medium text-sm">Masjid Ar Rahman, Carr Road, Auckland</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Form -->
                    <div class="bg-white/80 border border-primary shadow-lg overflow-hidden" id="contact-form">
                        <div class="p-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">Send Us a Message</h2>

                            <form id="contactForm" class="space-y-5">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                                    <div>
                                        <label for="firstName" class="block text-gray-700 font-medium mb-2">First Name</label>
                                        <input type="text" id="firstName" name="firstName" class="w-full px-4 py-3 border border-gray-300 form-input" required>
                                    </div>
                                    <div>
                                        <label for="lastName" class="block text-gray-700 font-medium mb-2">Last Name</label>
                                        <input type="text" id="lastName" name="lastName" class="w-full px-4 py-3 border border-gray-300 form-input" required>
                                    </div>
                                </div>

                                <div class="relative">
                                    <label for="email" class="block text-gray-700 font-medium mb-2">Email Address</label>
                                    <div class="relative">
                                        <input type="email" id="email" name="email" class="w-full px-4 py-3 pr-12 border border-gray-300 form-input" required>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <i id="email-status-icon" class="fas fa-circle text-green-500 hidden"></i>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label for="subject" class="block text-gray-700 font-medium mb-2">Subject</label>
                                    <input type="text" id="subject" name="subject" class="w-full px-4 py-3 border border-gray-300 form-input" required>
                                </div>

                                <div>
                                    <label for="message" class="block text-gray-700 font-medium mb-2">Your Message</label>
                                    <textarea id="message" name="message" rows="5" class="w-full px-4 py-3 border border-gray-300 form-input" required></textarea>
                                </div>

                                <div class="flex items-start mb-2">
                                    <input type="checkbox" id="privacy" name="privacy" class="mt-1 mr-2" required>
                                    <label for="privacy" class="text-sm text-gray-600">
                                        I am happy to be emailed by Khuddam Ul Quran regarding my inquiry.
                                    </label>
                                </div>

                                <div>
                                    <button type="button" id="submitContactForm" class="w-full bg-primary hover:bg-primary/50 text-black font-medium px-6 py-3 transition-all duration-300 flex items-center justify-center">
                                        <span>Send Message</span>
                                        <i class="fas fa-paper-plane ml-2"></i>
                                    </button>
                                </div>
                            </form>

                            <!-- Success Message (Hidden by default) -->
                            <div id="formSuccessMessage" class="hidden mt-6 p-4 bg-green-100 border border-green-200 text-green-700 text-center">
                                <i class="fas fa-check-circle text-2xl mb-2"></i>
                                <h3 class="text-lg font-bold">Message Sent Successfully!</h3>
                                <p>Thank you for contacting us. We'll get back to you as soon as possible.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50 -mt-1 relative overflow-hidden">
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto">
                <!-- Section Header -->
                <div class="text-center mb-12">
                    <h2 class="text-4xl font-bold text-gray-800 mb-4">Frequently Asked Questions</h2>
                    <div class="w-24 h-1 bg-primary mx-auto mb-6"></div>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        Find answers to common questions about our Quranic Arabic Grammar Learning Programme
                    </p>
                </div>

                <!-- FAQ Accordion -->
                <div class="space-y-4" id="faq-accordion">
                    <!-- FAQ Item 1 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-1">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">What is the Quranic Arabic Grammar Learning Programme?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-1" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    Our Quranic Arabic Grammar Learning Programme is a structured course designed to help adult male students understand the grammatical structure of the Arabic language as used in the Quran. The programme focuses on making complex grammar concepts accessible through a unique teaching methodology that connects directly to Quranic verses.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-2">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">Do I need prior knowledge of Arabic to join?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-2" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    No prior knowledge of Arabic is required to join our programme. We start from the basics and gradually build up your understanding. Our teaching approach is designed to accommodate beginners while still being valuable for those with some prior exposure to the language.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-3">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">How long does the programme last?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-3" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    The complete programme typically runs for 12 months, with classes held once a week. Each session lasts for one hour. The programme is divided into three levels: beginner, intermediate, and advanced. Students progress through these levels at their own pace, with assessments at the end of each level.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-4">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">Is there any fee for the programme?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-4" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    No, our programme is completely free of cost. We believe in making Quranic education accessible to all. However, registration is required as we have limited seats available for each batch. Students are expected to commit to regular attendance and participation.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 5 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-5">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">What materials will I need for the classes?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-5" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    All essential study materials are provided as part of the programme. This includes handouts, worksheets, and digital resources. Students are advised to bring a notebook, pen, and a copy of the Quran (if available). We also provide access to our online learning portal where additional resources and practice exercises are available.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 6 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-6">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">What if I miss a class?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-6" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    We understand that occasional absences may occur. For missed classes, we provide summary notes and exercises. However, regular attendance is strongly encouraged for the best learning experience. If you know in advance that you'll miss a class, please inform your instructor so they can help you stay on track with the curriculum.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 7 -->
                    <div class="faq-item bg-white border border-gray-200 shadow-sm transition-all duration-300">
                        <button class="faq-button w-full flex justify-between items-center p-5 text-left transition-all duration-300" data-target="faq-7">
                            <span class="faq-question text-lg font-medium text-gray-800 transition-colors duration-300">Is there a certificate upon completion?</span>
                            <i class="fas fa-chevron-down text-primary transition-transform duration-300"></i>
                        </button>
                        <div id="faq-7" class="faq-content overflow-hidden transition-all duration-300 ease-in-out max-h-0">
                            <div class="px-5 pb-5">
                                <p class="text-gray-600">
                                    Yes, students who successfully complete the programme receive a certificate of completion from Khuddam Ul Quran. This certificate recognizes your achievement in mastering Quranic Arabic grammar fundamentals. To qualify for the certificate, students must attend at least 80% of the classes and pass the final assessment.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Help Text -->
                <div class="text-center mt-12">
                    <p class="text-gray-600">
                        Don't see your question here? <a href="#contact-form" class="text-primary font-medium hover:underline">Contact us</a> and we'll be happy to help.
                    </p>
                </div>
            </div>
        </div>

        <!-- FAQ JavaScript -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Get all FAQ buttons
                const faqButtons = document.querySelectorAll('.faq-button');

                // Function to close all FAQs
                function closeAllFAQs() {
                    document.querySelectorAll('.faq-content').forEach(content => {
                        content.style.maxHeight = '0px';

                        // Reset button styles
                        const button = document.querySelector(`[data-target="${content.id}"]`);
                        if (button) {
                            const faqItem = button.closest('.faq-item');
                            const faqQuestion = button.querySelector('.faq-question');
                            const icon = button.querySelector('i');

                            // Reset colors and border
                            faqItem.classList.remove('border-primary');
                            faqItem.classList.add('border-gray-200');
                            faqQuestion.classList.remove('text-primary');
                            faqQuestion.classList.add('text-gray-800');

                            // Reset icon rotation
                            if (icon) {
                                icon.classList.remove('transform', 'rotate-180');
                            }
                        }
                    });
                }

                // Function to open a specific FAQ
                function openFAQ(targetId, button) {
                    const content = document.getElementById(targetId);
                    const faqItem = button.closest('.faq-item');
                    const faqQuestion = button.querySelector('.faq-question');
                    const icon = button.querySelector('i');

                    // Set max-height to the scroll height for smooth animation
                    content.style.maxHeight = content.scrollHeight + 'px';

                    // Update colors and border
                    faqItem.classList.remove('border-gray-200');
                    faqItem.classList.add('border-primary');
                    faqQuestion.classList.remove('text-gray-800');
                    faqQuestion.classList.add('text-primary');

                    // Rotate icon
                    if (icon) {
                        icon.classList.add('transform', 'rotate-180');
                    }
                }

                // Add click event to each button
                faqButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const targetId = this.getAttribute('data-target');
                        const content = document.getElementById(targetId);
                        const isCurrentlyOpen = content.style.maxHeight && content.style.maxHeight !== '0px';

                        if (isCurrentlyOpen) {
                            // Close this FAQ
                            closeAllFAQs();
                        } else {
                            // Close all other FAQs first
                            closeAllFAQs();

                            // Then open this FAQ
                            setTimeout(() => {
                                openFAQ(targetId, this);
                            }, 100); // Small delay to ensure smooth transition
                        }
                    });
                });

                // Open the first FAQ by default
                if (faqButtons.length > 0) {
                    setTimeout(() => {
                        openFAQ('faq-1', faqButtons[0]);
                    }, 300);
                }
            });
        </script>
    </section>


    <!-- Footer -->
    <footer class="bg-dark/95 shadow-lg text-white -mt-1">
        <div class="container mx-auto px-4 py-10">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Logos -->
                <div class="mb-6 md:mb-0 flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="bg-white rounded-sm mr-2">
                            <img src="images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12">
                        </div>
                    </div>
                    <div class="flex items-center">
                        <a href="/">
                            <img src="images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12">
                        </a>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="news.html" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="resources.html" class="text-white font-medium hover:text-primary transition">Resources</a>
                    <a href="registration-form.html" class="text-white font-medium hover:text-primary transition">Register Now</a>
                    <a href="contact-form.html" class="text-primary font-medium hover:text-white transition">Contact</a>
                </div>

                <!-- Social Media -->
                <div class="flex space-x-4">
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://www.youtube.com/@KhuddamUlQuranNZ" target="_blank" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-white/10 mt-8 pt-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
                    <div class="mt-4 md:mt-0">
                        <ul class="flex space-x-6 text-sm">
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Developer Credits -->
                <div class="mt-4 pt-4 border-t border-white/5">
                    <div class="text-center">
                        <p class="text-white/50 text-xs">
                            Website developed by
                            <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                                Kiwiorbit
                            </a>
                            | Contact:
                            <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                                +64 22 325 9094
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js" defer></script>
    </div><!-- End of main-content -->

    <!-- Contact Form Submission Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Contact Form Submission
            const contactForm = document.getElementById('contactForm');
            const submitButton = document.getElementById('submitContactForm');
            const successMessage = document.getElementById('formSuccessMessage');

            // Function to validate email format
            function validateEmail(email) {
                // Comprehensive email validation regex following RFC 5322 standards
                const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return emailRegex.test(email);
            }

            // Function to get specific email error message
            function getEmailErrorMessage(email) {
                if (!email.includes('@')) {
                    return 'Email address must contain an @ symbol';
                } else if (email.indexOf('@') === 0) {
                    return 'Email address cannot start with an @ symbol';
                } else if (email.indexOf('@') === email.length - 1) {
                    return 'Please include a domain after the @ symbol';
                } else if (!email.substring(email.indexOf('@') + 1).includes('.')) {
                    return 'Please include a valid domain with a dot (e.g., .com, .org)';
                } else if (email.substring(email.lastIndexOf('.')).length < 2) {
                    return 'Please include a valid domain extension (.com, .org, etc.)';
                } else {
                    return 'Please enter a valid email address';
                }
            }

            // Function to validate form
            function validateContactForm() {
                const requiredFields = contactForm.querySelectorAll('[required]');
                let isValid = true;

                // Remove any existing error messages
                const existingErrors = contactForm.querySelectorAll('.error-message');
                existingErrors.forEach(error => error.remove());

                // Check each required field
                requiredFields.forEach(field => {
                    field.classList.remove('border-red-500');

                    if (field.type === 'checkbox' && !field.checked) {
                        isValid = false;
                        field.classList.add('border-red-500');

                        // Add error message for checkbox
                        const errorMessage = document.createElement('p');
                        errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
                        errorMessage.textContent = 'Please agree to the privacy policy';
                        field.parentNode.appendChild(errorMessage);
                    } else if (field.type !== 'checkbox' && !field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');

                        // Add error message
                        const errorMessage = document.createElement('p');
                        errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
                        errorMessage.textContent = 'This field is required';
                        field.parentNode.appendChild(errorMessage);
                    }
                    // Special validation for email field
                    else if (field.id === 'email' && field.value.trim()) {
                        const emailStatusIcon = document.getElementById('email-status-icon');

                        if (!validateEmail(field.value.trim())) {
                            isValid = false;
                            field.classList.add('border-red-500');

                            // Hide the status icon on error
                            if (emailStatusIcon) {
                                emailStatusIcon.classList.add('hidden');
                            }

                            // Add specific email error message
                            const errorMessage = document.createElement('p');
                            errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
                            errorMessage.textContent = getEmailErrorMessage(field.value.trim());
                            field.parentNode.parentNode.appendChild(errorMessage);
                        } else {
                            // Email is valid - show green circle
                            if (emailStatusIcon) {
                                emailStatusIcon.classList.remove('hidden');
                                emailStatusIcon.classList.add('text-green-500');
                                emailStatusIcon.classList.remove('text-red-500');
                            }
                        }
                    }
                });

                return isValid;
            }

            // Function to submit form to Google Form
            function submitToGoogleForm() {
                // Get form values
                const firstName = document.getElementById('firstName').value;
                const lastName = document.getElementById('lastName').value;
                const email = document.getElementById('email').value;
                const subject = document.getElementById('subject').value;
                const message = document.getElementById('message').value;

                // Create a hidden form that will submit to Google Forms
                const hiddenForm = document.createElement('form');
                hiddenForm.method = 'POST';
                hiddenForm.action = 'https://docs.google.com/forms/d/e/1FAIpQLSeUxzJkW2U1Z-z2sEtUMzHWsINdgNNewRzYBuuAdEt19HujfQ/formResponse';
                hiddenForm.target = 'hidden-iframe'; // Target the iframe
                hiddenForm.style.display = 'none';

                // Create and append input fields
                const firstNameInput = document.createElement('input');
                firstNameInput.name = 'entry.1347769261';
                firstNameInput.value = firstName;
                hiddenForm.appendChild(firstNameInput);

                const lastNameInput = document.createElement('input');
                lastNameInput.name = 'entry.1775621398';
                lastNameInput.value = lastName;
                hiddenForm.appendChild(lastNameInput);

                const emailInput = document.createElement('input');
                emailInput.name = 'entry.662066445';
                emailInput.value = email;
                hiddenForm.appendChild(emailInput);

                const subjectInput = document.createElement('input');
                subjectInput.name = 'entry.634621754';
                subjectInput.value = subject;
                hiddenForm.appendChild(subjectInput);

                const messageInput = document.createElement('input');
                messageInput.name = 'entry.735932825';
                messageInput.value = message;
                hiddenForm.appendChild(messageInput);

                // Create hidden iframe to prevent page navigation
                let iframe = document.getElementById('hidden-iframe');
                if (!iframe) {
                    iframe = document.createElement('iframe');
                    iframe.name = 'hidden-iframe';
                    iframe.id = 'hidden-iframe';
                    iframe.style.display = 'none';
                    document.body.appendChild(iframe);
                }

                // Append form to body, submit it, and remove it
                document.body.appendChild(hiddenForm);
                hiddenForm.submit();

                // Clean up after submission
                setTimeout(() => {
                    document.body.removeChild(hiddenForm);
                }, 500);
            }

            // Add real-time email validation
            const emailField = document.getElementById('email');
            const emailStatusIcon = document.getElementById('email-status-icon');

            if (emailField) {
                emailField.addEventListener('blur', function() {
                    if (this.value.trim() !== '') {
                        const email = this.value.trim();

                        // Remove any existing error messages for this field
                        const existingError = this.parentNode.parentNode.querySelector('.error-message');
                        if (existingError) {
                            existingError.remove();
                        }

                        // Remove error styling
                        this.classList.remove('border-red-500');

                        // Validate email
                        if (validateEmail(email)) {
                            // Email is valid - show green circle
                            if (emailStatusIcon) {
                                emailStatusIcon.classList.remove('hidden');
                                emailStatusIcon.classList.add('text-green-500');
                                emailStatusIcon.classList.remove('text-red-500');
                            }
                        } else {
                            // Email is invalid - hide icon and show error
                            if (emailStatusIcon) {
                                emailStatusIcon.classList.add('hidden');
                            }

                            this.classList.add('border-red-500');

                            // Add specific error message
                            const errorMessage = document.createElement('p');
                            errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
                            errorMessage.textContent = getEmailErrorMessage(email);
                            this.parentNode.parentNode.appendChild(errorMessage);
                        }
                    } else {
                        // Empty field - hide icon
                        if (emailStatusIcon) {
                            emailStatusIcon.classList.add('hidden');
                        }
                    }
                });

                // Clear error and icon when user starts typing again
                emailField.addEventListener('input', function() {
                    // Hide the status icon while typing
                    if (emailStatusIcon) {
                        emailStatusIcon.classList.add('hidden');
                    }

                    // Remove error styling and messages
                    if (this.classList.contains('border-red-500')) {
                        this.classList.remove('border-red-500');
                        const existingError = this.parentNode.parentNode.querySelector('.error-message');
                        if (existingError) {
                            existingError.remove();
                        }
                    }
                });
            }

            // Add click event to submit button
            if (submitButton) {
                submitButton.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Validate form
                    if (validateContactForm()) {
                        // Submit to Google Form
                        submitToGoogleForm();

                        // Show success message
                        contactForm.classList.add('hidden');
                        successMessage.classList.remove('hidden');

                        // Reset form
                        contactForm.reset();

                        // Scroll to success message
                        successMessage.scrollIntoView({ behavior: 'smooth' });
                    } else {
                        // Scroll to first error
                        const firstError = contactForm.querySelector('.error-message');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth' });
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
