// Community Posts Data and Management
// ===================================
// This file contains all community posts data and related functionality
// To add new posts, simply add them to the communityPosts array below
// Use the next available ID number (currently next: 9)
// Place post images in the 'news-images/' folder

// Community posts data
// NOTE: Posts are automatically sorted by ID (highest ID = most recent)
// When adding new posts, use the next available ID number (currently next: 9)
const communityPosts = [
    {
        id: 1,
        title: 'iERA Dawah Training Programme',
        description: 'This 10-week training course equips Muslims with the tools and techniques needed for effective Islamic outreach in today’s society. Designed to build confidence and wisdom in sharing the faith, the program combines theory with practical exercises.\n' +
            '\n' +
            'Key topics include:\n' +
            '\n' +
            'The psychology of effective communication\n' +
            '\n' +
            'Addressing common misconceptions about Islam\n' +
            '\n' +
            'Developing respectful dialogue skills\n' +
            '\n' +
            'Positive messaging through social media\n' +
            '\n' +
            'Contextualizing Islam for diverse audiences',
        date: '2025-01-30',
        image: 'news-images/dawah-stall.jpg'
    },
    {
        id: 2,
        title: 'Nouman Ali Khan Lecture for Young Minds',
        description: 'Special lecture series designed for young Muslims featuring insights from renowned Islamic scholar Nouman <PERSON>. Topics include understanding the Quran in modern context, building strong Islamic identity, and navigating contemporary challenges with Islamic wisdom.',
        date: '2025-01-25',
        image: 'news-images/naksession.webp'
    },
    {
        id: 3,
        title: 'Youth Leadership Workshop',
        description: 'Our recent youth leadership workshop equipped young Muslims with essential skills for community engagement and Islamic advocacy. This three-day intensive program was designed to empower the next generation of Muslim leaders.\n' +
            '\n' +
            'The workshop covered:\n' +
            '\n' +
            'Public speaking and presentation skills\n' +
            'Community organizing and event planning\n' +
            'Islamic principles of leadership and service\n' +
            'Media engagement and dawah techniques\n' +
            'Project management and team building',
        date: '2025-02-01',
        image: 'news-images/youth1.jpg'
    },
    {
        id: 4,
        title: 'Meet the Team behind the Scenes',
        description: 'Alhamdulillah! Here\'s a glimpse of the dedicated contributors and members of Khuddam Al-Qur’an—those working tirelessly behind the scenes to serve the Qur’an and our community. From organizing programs to teaching and outreach, each person plays a vital role in our mission to deepen our connection with the Qur’an.\n' +
            '\n' +
            'May Allah accept their efforts and grant them barakah in their work.',
        date: '2025-05-20',
        image: 'news-images/teammembers.webp'
    },
    {
        id: 5,
        title: 'Sunday Fajr Quran Classes',
        description: 'Join us every Sunday morning after Fajr for a spiritually enriching Qur\'an class hosted by Khuddam Al-Qur\'an. In these sessions, attendees gather to listen to the powerful and insightful teachings of Dr. Israr Ahmad, deepening their understanding of the Qur\'an and its message. All are welcome to attend and benefit from this weekly journey of reflection and learning. We gather at Masjid Ar Rahman for this blessed weekly gathering.',
        date: '2025-01-19',
        image: 'news-images/quranclass.webp'
    },
    {
        id: 6,
        title: 'Quranic Arabic Grammar Programme - Enrollment Open',
        description: 'Limited seats available for our comprehensive Arabic Grammar Learning Programme. Early bird registration ends soon! Join us for an intensive journey into understanding the language of the Quran. This program is designed for male adults who want to deepen their connection with the Quran through linguistic understanding.',
        date: '2025-05-25',
        image: 'news-images/enrollment-poster.webp'
    },
    {
        id: 7,
        title: 'Author Visit: Mr. James at Masjid Ar Rahman',
        description: 'We were honored to welcome Mr. James to Masjid Ar Rahman as part of his research for an upcoming book. The story centers on a young Muslim practicing Ramadan and exploring Islamic culture while growing up in New Zealand. His visit allowed him to engage with our community, gain insight into Ramadan traditions, and experience the warmth and spirit of the holy month. We look forward to the positive representation this book will bring and appreciate his respectful interest in our faith and practices.',
        date: '2025-05-25',
        image: 'news-images/newbook.webp'
    },
    {
        id: 8,
        title: 'Digital Quranic Dictionary App Now Available',
        description: 'Alhamdulillah! We are excited to announce the launch of our Digital Quranic Dictionary app, now available for both iOS and Android devices. This comprehensive dictionary is based on the work of Lutfur Rahman Sb (late) and published by Al Balagh Foundation Lahore, Pakistan.\n' +
            '\n' +
            'For the sake of Allah SWT, Khuddam-ul-Quran NZ has digitized this valuable resource to benefit all Arabic grammar students in searching the meanings of Quranic words quickly and efficiently.\n' +
            '\n' +
            'Key Features:\n' +
            '• Contains root letters with 3 and 4 letters used in the Quran\n' +
            '• Quick search functionality for Quranic word meanings\n' +
            '• Relevant Quranic verses mentioned for further clarification\n' +
            '• Designed specifically for Arabic grammar students\n' +
            '• Available on both iOS and Android platforms\n' +
            '\n' +
            'Download now from the App Store or Google Play Store. May Allah SWT accept our efforts. Ameen.',
        date: '2025-07-04',
        image: 'news-images/andriod.webp'
    }
];







// ===================================
// COMMUNITY POSTS MANAGEMENT SYSTEM
// ===================================

// Sort posts by ID in descending order (highest ID first = most recent)
const sortedPosts = [...communityPosts].sort((a, b) => b.id - a.id);

// Pagination settings
const postsPerPage = 6;
let currentPage = 1;
let currentSearchTerm = '';
let filteredPosts = [...sortedPosts];

// DOM elements
let postsGrid, paginationContainer, searchInput, clearSearchBtn;
let searchResultsInfo, resultsCount, noResults, postModal, modalBackdrop, closeModalBtn;

// Initialize community posts system
function initializeCommunityPosts() {
    // Get DOM elements
    postsGrid = document.getElementById('posts-grid');
    paginationContainer = document.getElementById('pagination-container');
    searchInput = document.getElementById('search-input');
    clearSearchBtn = document.getElementById('clear-search');
    searchResultsInfo = document.getElementById('search-results-info');
    resultsCount = document.getElementById('results-count');
    noResults = document.getElementById('no-results');
    postModal = document.getElementById('post-modal');
    modalBackdrop = document.getElementById('modal-backdrop');
    closeModalBtn = document.getElementById('close-modal');

    // Render initial content
    renderPosts();
    renderPagination();
    setupEventListeners();

    // Hide loader and show content
    setTimeout(() => {
        document.getElementById('loader').style.display = 'none';
        document.getElementById('main-content').style.opacity = '1';
    }, 1000);
}

// Setup event listeners
function setupEventListeners() {
    // Search input
    searchInput.addEventListener('input', handleSearch);
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch();
        }
    });

    // Clear search button
    clearSearchBtn.addEventListener('click', clearSearch);

    // Modal close events
    closeModalBtn.addEventListener('click', closeModal);
    modalBackdrop.addEventListener('click', closeModal);

    // Close modal on escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') closeModal();
    });
}

// Handle search
function handleSearch() {
    const searchTerm = searchInput.value.trim().toLowerCase();
    currentSearchTerm = searchTerm;
    currentPage = 1;

    if (searchTerm === '') {
        filteredPosts = [...sortedPosts];
        hideSearchResults();
    } else {
        filteredPosts = sortedPosts.filter(post => {
            return post.title.toLowerCase().includes(searchTerm) ||
                   post.description.toLowerCase().includes(searchTerm);
        });
        showSearchResults();
    }

    // Show/hide clear button
    if (searchTerm) {
        clearSearchBtn.classList.remove('hidden');
    } else {
        clearSearchBtn.classList.add('hidden');
    }

    renderPosts();
    renderPagination();
}

// Clear search
function clearSearch() {
    searchInput.value = '';
    currentSearchTerm = '';
    filteredPosts = [...sortedPosts];
    clearSearchBtn.classList.add('hidden');
    hideSearchResults();
    renderPosts();
    renderPagination();
    searchInput.focus();
}

// Show search results info
function showSearchResults() {
    const count = filteredPosts.length;
    if (count === 0) {
        searchResultsInfo.classList.add('hidden');
        noResults.classList.remove('hidden');
    } else {
        resultsCount.textContent = `Found ${count} post${count !== 1 ? 's' : ''} matching "${currentSearchTerm}"`;
        searchResultsInfo.classList.remove('hidden');
        noResults.classList.add('hidden');
    }
}

// Hide search results info
function hideSearchResults() {
    searchResultsInfo.classList.add('hidden');
    noResults.classList.add('hidden');
}

// Render posts
function renderPosts() {
    const startIndex = (currentPage - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
    const postsToShow = filteredPosts.slice(startIndex, endIndex);

    postsGrid.innerHTML = '';

    postsToShow.forEach((post, index) => {
        const postCard = createPostCard(post, startIndex + index);
        postsGrid.appendChild(postCard);
    });
}

// Create post card
function createPostCard(post, index) {
    const card = document.createElement('div');
    card.className = 'post-card bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer';

    const formattedDate = new Date(post.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    let truncatedDescription = post.description.length > 120
        ? post.description.substring(0, 120) + '...'
        : post.description;

    // Highlight search terms
    let highlightedTitle = post.title;
    let highlightedDescription = truncatedDescription;

    if (currentSearchTerm) {
        const regex = new RegExp(`(${currentSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        highlightedTitle = post.title.replace(regex, '<span class="search-highlight">$1</span>');
        highlightedDescription = truncatedDescription.replace(regex, '<span class="search-highlight">$1</span>');
    }

    card.innerHTML = `
        <div class="relative aspect-square">
            <img src="${post.image}" alt="${post.title}" class="w-full h-full object-cover">
        </div>
        <div class="p-6">
            <h3 class="text-xl font-bold text-dark mb-3 line-clamp-2">${highlightedTitle}</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">${highlightedDescription}</p>
            <div class="flex items-center text-sm text-gray-500 mb-4">
                <span><i class="fas fa-calendar mr-1"></i>${formattedDate}</span>
            </div>
            <button class="read-more-btn text-primary hover:text-primary/80 font-medium transition-all duration-300 flex items-center">
                <span>Read more</span>
                <i class="fas fa-arrow-right ml-2"></i>
            </button>
        </div>
    `;

    // Add click event to open modal
    card.addEventListener('click', () => openModal(post));

    return card;
}

// Render pagination
function renderPagination() {
    const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
    paginationContainer.innerHTML = '';

    if (totalPages <= 1) return;

    // Previous button
    if (currentPage > 1) {
        const prevBtn = createPaginationButton('Previous', currentPage - 1);
        prevBtn.innerHTML = '<i class="fas fa-chevron-left mr-2"></i>Previous';
        paginationContainer.appendChild(prevBtn);
    }

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const pageBtn = createPaginationButton(i, i);
        if (i === currentPage) {
            pageBtn.classList.add('active');
        }
        paginationContainer.appendChild(pageBtn);
    }

    // Next button
    if (currentPage < totalPages) {
        const nextBtn = createPaginationButton('Next', currentPage + 1);
        nextBtn.innerHTML = 'Next<i class="fas fa-chevron-right ml-2"></i>';
        paginationContainer.appendChild(nextBtn);
    }
}

// Create pagination button
function createPaginationButton(text, page) {
    const btn = document.createElement('button');
    btn.className = 'pagination-btn px-4 py-2 mx-1 border border-gray-300 text-gray-700 hover:bg-primary hover:text-black transition-all duration-300 rounded';
    btn.textContent = text;
    btn.addEventListener('click', () => {
        currentPage = page;
        renderPosts();
        renderPagination();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
    return btn;
}

// Open modal
function openModal(post) {
    const modalTitle = document.getElementById('modal-title');
    const modalImage = document.getElementById('modal-image');
    const modalDate = document.getElementById('modal-date');
    const modalMeta = document.getElementById('modal-meta');
    const modalDescription = document.getElementById('modal-description');
    const modalContentWrapper = document.getElementById('modal-content-wrapper');

    const formattedDate = new Date(post.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // Populate modal content
    modalTitle.textContent = post.title;
    modalImage.src = post.image;
    modalImage.alt = post.title;

    modalDate.innerHTML = `<i class="fas fa-calendar text-primary mr-2"></i>${formattedDate}`;

    // Add meta information (no location or time)
    modalMeta.innerHTML = '';

    modalDescription.textContent = post.description;

    // Function to resize modal based on image dimensions
    function resizeModal() {
        const img = new Image();
        img.onload = function() {
            const imageWidth = this.naturalWidth;
            const imageHeight = this.naturalHeight;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Calculate optimal modal width based on image width
            let modalWidth = imageWidth + 48; // Add padding (24px * 2)

            // Apply responsive constraints
            if (viewportWidth <= 640) {
                // Mobile: use 95% of viewport width
                modalWidth = Math.min(modalWidth, viewportWidth * 0.95);
            } else if (viewportWidth <= 1024) {
                // Tablet: use 85% of viewport width
                modalWidth = Math.min(modalWidth, viewportWidth * 0.85);
            } else {
                // Desktop: limit to reasonable max width for readability
                const maxDesktopWidth = Math.min(800, viewportWidth * 0.6);
                modalWidth = Math.min(modalWidth, maxDesktopWidth);
            }

            // Ensure minimum width
            modalWidth = Math.max(modalWidth, 320);

            // Set the modal width
            modalContentWrapper.style.width = modalWidth + 'px';
        };
        img.src = post.image;
    }

    // Show modal
    postModal.classList.remove('hidden');
    setTimeout(() => {
        postModal.classList.remove('opacity-0');
        resizeModal(); // Resize after modal is visible
    }, 10);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModal() {
    postModal.classList.add('opacity-0');
    setTimeout(() => {
        postModal.classList.add('hidden');
        // Reset modal width
        const modalContentWrapper = document.getElementById('modal-content-wrapper');
        modalContentWrapper.style.width = 'auto';
    }, 300);

    // Restore body scroll
    document.body.style.overflow = 'auto';
}
