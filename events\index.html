<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events - Khuddam | Servants of the Quran</title>
    <meta name="description" content="Explore our event gallery showcasing memorable moments from Khuddam events, classes, and community gatherings. Browse photos from our Quranic Arabic learning sessions and Islamic events.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://khuddam.co.nz/events/">
    <meta property="og:title" content="Khuddam Gallery - Event Photos & Memories">
    <meta property="og:description" content="Explore our event gallery showcasing memorable moments from Khuddam events, classes, and community gatherings.">
    <meta property="og:image" content="https://picsum.photos/600/400?random=1">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://khuddam.co.nz/events/">
    <meta name="twitter:title" content="Khuddam Gallery - Event Photos & Memories">
    <meta name="twitter:description" content="Explore our event gallery showcasing memorable moments from Khuddam events, classes, and community gatherings.">
    <meta name="twitter:image" content="https://picsum.photos/600/400?random=1">

    <!-- Critical CSS loaded directly for faster rendering -->
    <link rel="stylesheet" href="../css/critical.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- External CSS resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <style>
        /* Custom styles for event cards */
        .event-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .event-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .event-image {
            transition: transform 0.3s ease;
        }

        .event-card:hover .event-image {
            transform: scale(1.05);
        }

        /* Loading animation */
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #deae35;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Category badges */
        .category-badge {
            background: linear-gradient(135deg, #deae35 0%, #f4c842 100%);
        }
    </style>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ImageGallery",
        "name": "Khuddam Event Gallery",
        "description": "Photo gallery showcasing events and activities from Khuddam - Servants of the Quran",
        "url": "https://khuddam.co.nz/events/",
        "publisher": {
            "@type": "Organization",
            "name": "Khuddam - Servants of the Quran",
            "url": "https://khuddam.co.nz/"
        }
    }
    </script>
</head>

<body class="bg-gray-50 font-sans text-dark">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 bg-dark z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-white text-lg">Loading Events...</p>
        </div>
    </div>

    <div id="main-content" style="opacity: 0;">
        <!-- Header with Navigation -->
        <header class="bg-dark/95 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
            <div class="container mx-auto px-4 py-3">
                <nav class="flex items-center justify-between">
                    <div class="flex items-center">
                        <a href="/">
                            <img src="../images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                        <a href="/about/" class="text-white font-medium hover:text-primary transition">About Us</a>
                        <a href="/news/" class="text-white font-medium hover:text-primary transition">News</a>
                        <a href="/events/" class="text-primary font-medium hover:text-white transition">Events</a>
                        <a href="/resources/" class="text-white font-medium hover:text-primary transition">Resources</a>
                        <a href="/contact_form/" class="text-white font-medium hover:text-primary transition">Contact</a>
                        <a href="/registration_form/" class="border border-primary px-6 py-2 text-white font-medium hover:text-primary transition flex items-center justify-center">Register Now</a>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-button" class="md:hidden text-white hover:text-primary transition">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </nav>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="fixed inset-0 bg-dark/95 z-40 hidden">
                <div class="flex items-center justify-between p-4">
                    <img src="../images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                    <button id="mobile-menu-close" class="text-white hover:text-primary transition">
                        <i class="fas fa-times text-2xl"></i>
                    </button>
                </div>

                <!-- Menu content -->
                <div class="flex flex-col items-center justify-center h-full w-full">
                    <div class="flex flex-col items-center space-y-10 py-8">
                        <a href="/" class="text-white text-2xl font-medium hover:text-primary transition">Home</a>
                        <a href="/about/" class="text-white text-2xl font-medium hover:text-primary transition">About Us</a>
                        <a href="/news/" class="text-white text-2xl font-medium hover:text-primary transition">News</a>
                        <a href="/events/" class="text-primary text-2xl font-medium hover:text-white transition">Events</a>
                        <a href="/resources/" class="text-white text-2xl font-medium hover:text-primary transition">Resources</a>
                        <a href="/contact_form/" class="text-white text-2xl font-medium hover:text-primary transition">Contact</a>
                        <a href="/registration_form/" class="text-white text-2xl font-medium hover:text-primary transition">Register Now</a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="relative bg-dark/95 text-white pt-32 pb-16">
            <div class="container mx-auto px-4">
                <div class="text-center max-w-4xl mx-auto" data-aos="fade-up">
                    <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                        Event
                        <span class="text-primary">Gallery</span>
                    </h1>
                    <p class="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
                        Explore our collection of memorable moments from Quranic Arabic learning sessions, community events, and Islamic gatherings
                    </p>
                    <div class="w-24 h-1 bg-primary mx-auto mb-8"></div>
                    <div class="flex justify-center space-x-6">
                        <div class="inline-flex items-center px-4 py-2 bg-white/10 rounded-sm">
                            <i class="fas fa-calendar text-primary mr-2"></i>
                            <span class="text-white/90" id="total-events-count">Loading events...</span>
                        </div>
                        <div class="inline-flex items-center px-4 py-2 bg-white/10 rounded-sm">
                            <i class="fas fa-images text-primary mr-2"></i>
                            <span class="text-white/90" id="total-photos-count">Loading photos...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Events Grid -->
        <section class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <div id="events-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Event cards will be dynamically loaded here -->
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-dark/95 shadow-md text-white -mt-1">
            <div class="container mx-auto px-4 py-10">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <!-- Logos -->
                    <div class="mb-6 md:mb-0 flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="mr-2">
                                <img src="../images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12">
                            </div>
                        </div>
                        <div class="flex items-center">
                            <a href="/">
                                <img src="../images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12">
                            </a>
                        </div>
                    </div>

                    <!-- Navigation Links -->
                    <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
                        <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                        <a href="/about/" class="text-white font-medium hover:text-primary transition">About Us</a>
                        <a href="/news/" class="text-white font-medium hover:text-primary transition">News</a>
                        <a href="/events/" class="text-primary font-medium hover:text-white transition">Events</a>
                        <a href="/resources/" class="text-white font-medium hover:text-primary transition">Resources</a>
                        <a href="/registration_form/" class="text-white font-medium hover:text-primary transition">Register Now</a>
                        <a href="/contact_form/" class="text-white font-medium hover:text-primary transition">Contact</a>
                    </div>

                    <!-- Social Media -->
                    <div class="flex space-x-4">
                        <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="https://www.youtube.com/@KhuddamUlQuranNZ" target="_blank" class="text-white/70 hover:text-primary transition-all duration-300">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>

                <div class="border-t border-white/10 mt-8 pt-6">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
                        <div class="mt-4 md:mt-0">
                            <ul class="flex space-x-6 text-sm">
                                <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
                                <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Developer Credits -->
                    <div class="mt-4 pt-4 border-t border-white/5">
                        <div class="text-center">
                            <p class="text-white/50 text-xs">
                                Website developed by
                                <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                                    Kiwiorbit
                                </a>
                                | Contact:
                                <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                                    +64 22 325 9094
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <script src="../js/events-posts.js"></script>
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Gallery events page initialization
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventsGallery();
        });

        function initializeEventsGallery() {
            const eventsGrid = document.getElementById('events-grid');
            const totalEventsElement = document.getElementById('total-events-count');
            const totalPhotosElement = document.getElementById('total-photos-count');

            if (!eventsGrid) {
                console.error('Events grid element not found');
                return;
            }

            // Update counters
            if (totalEventsElement && typeof sortedEvents !== 'undefined') {
                totalEventsElement.textContent = `${sortedEvents.length} Events`;
            }
            if (totalPhotosElement && typeof totalPhotos !== 'undefined') {
                totalPhotosElement.textContent = `${totalPhotos} Photos`;
            }

            // Display event cards
            displayEventCards();

            // Hide loader and show content
            setTimeout(() => {
                const loader = document.getElementById('loader');
                const mainContent = document.getElementById('main-content');

                if (loader) {
                    loader.style.display = 'none';
                }
                if (mainContent) {
                    mainContent.style.opacity = '1';
                }
            }, 1000);
        }

        function displayEventCards() {
            const eventsGrid = document.getElementById('events-grid');
            if (!eventsGrid || typeof sortedEvents === 'undefined') return;

            eventsGrid.innerHTML = '';

            sortedEvents.forEach((event, index) => {
                const eventCard = createEventCard(event, index);
                eventsGrid.appendChild(eventCard);
            });
        }

        function createEventCard(event, index) {
            const card = document.createElement('div');
            card.className = 'event-card group relative overflow-hidden shadow-lg rounded-lg cursor-pointer transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl bg-white';
            card.setAttribute('data-aos', 'fade-up');
            card.setAttribute('data-aos-delay', (index % 3) * 100);

            const formattedDate = new Date(event.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const categoryColors = {
                graduation: 'bg-green-500',
                community: 'bg-blue-500',
                program: 'bg-purple-500',
                education: 'bg-orange-500'
            };

            card.innerHTML = `
                <div class="relative aspect-[4/3] overflow-hidden">
                    <img src="${event.coverImage}" alt="${event.title}" class="event-image w-full h-full object-cover transition-transform duration-300" loading="lazy">
                    <div class="absolute top-4 left-4">
                        <span class="category-badge text-black text-xs font-semibold px-3 py-1 rounded-full">
                            ${event.category.charAt(0).toUpperCase() + event.category.slice(1)}
                        </span>
                    </div>
                    <div class="absolute inset-0 bg-primary/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <div class="text-center text-white">
                            <i class="fas fa-images text-4xl mb-2"></i>
                            <p class="text-lg font-semibold">View ${event.photoCount} Photos</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-bold text-dark mb-2 group-hover:text-primary transition-colors">${event.title}</h3>
                    <div class="flex items-center text-secondary mb-3">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span class="text-sm">${formattedDate}</span>
                    </div>
                    <div class="flex items-center text-secondary mb-3">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <span class="text-sm">${event.location}</span>
                    </div>
                    <p class="text-secondary text-sm mb-4 line-clamp-3">${event.description}</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-primary">
                            <i class="fas fa-images mr-2"></i>
                            <span class="text-sm font-semibold">${event.photoCount} Photos</span>
                        </div>
                        <div class="flex items-center text-primary font-semibold">
                            <span class="text-sm mr-2">View Gallery</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </div>
            `;

            // Add click event to navigate to individual event gallery
            card.addEventListener('click', () => {
                window.location.href = `event-pages/${event.slug}.html`;
            });

            return card;
        }
    </script>
</body>
</html>
