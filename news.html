<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>News - Khuddam | Servants of the Quran</title>
  <meta name="description" content="Stay connected with the Khuddam community. Latest news on Fajr classes, lectures, events, and enrollment opportunities.">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://khuddam.co.nz/news.html">
  <meta property="og:title" content="Khuddam News - Latest Updates & Events">
  <meta property="og:description" content="Stay connected with the Khuddam community. Latest news on Fajr classes, lectures, events, and enrollment opportunities.">
  <meta property="og:image" content="images/enrollment post.png">

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:url" content="https://khuddam.co.nz/news.html">
  <meta name="twitter:title" content="Khuddam News - Latest Updates & Events">
  <meta name="twitter:description" content="Stay connected with the Khuddam community. Latest news on Fajr classes, lectures, events, and enrollment opportunities.">
  <meta name="twitter:image" content="images/enrollment post.png">

  <!-- Critical CSS loaded directly for faster rendering -->
  <link rel="stylesheet" href="css/critical.css">

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@next/dist/aos.css" rel="stylesheet">
  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#deae35',
            dark: '#101010'
          },
          fontFamily: {
            'arabic': ['Amiri', 'serif'],
            'sans': ['Poppins', 'sans-serif']
          }
        }
      }
    }
  </script>

  <style>
    /* Custom styles for community page */
    .post-card {
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .post-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .search-highlight {
      background-color: #fef3cd;
      padding: 2px 4px;
      border-radius: 3px;
    }

    .post-type-badge {
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .modal-backdrop {
      /* Removed blur effect */
    }

    /* Dynamic modal sizing */
    #modal-content-wrapper {
      min-width: 320px;
      max-width: 95vw;
      width: auto;
    }

    /* Responsive modal constraints */
    @media (max-width: 640px) {
      #modal-content-wrapper {
        width: 95vw;
        max-width: 95vw;
      }
    }

    @media (min-width: 641px) and (max-width: 1024px) {
      #modal-content-wrapper {
        max-width: 85vw;
      }
    }

    @media (min-width: 1025px) {
      #modal-content-wrapper {
        max-width: 80vw;
      }
    }

    .pagination-btn {
      transition: all 0.3s ease;
    }

    .pagination-btn:hover {
      transform: translateY(-2px);
    }

    .pagination-btn.active {
      background-color: #deae35;
      color: #000;
    }

    /* Light theme overrides */
    body {
      background-color: #f8f9fa;
    }

    .bg-light {
      background-color: #ffffff;
    }

    .text-dark-gray {
      color: #2d3748;
    }

    .text-gray {
      color: #4a5568;
    }

    .border-light {
      border-color: #e2e8f0;
    }

    @media (max-width: 767px) {
      .hidden.md\:flex { display: none; }
    }

    /* Loading animation */
    .loading-spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #deae35;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>

<body class="bg-gray-50 text-gray-800">
<!-- Loading Screen -->
<div id="loader" class="fixed inset-0 bg-dark z-50 flex items-center justify-center">
  <div class="text-center">
    <div class="loading-spinner mx-auto mb-4"></div>
    <p class="text-white text-lg">Loading Posts...</p>
  </div>
</div>

<!-- Main Content Wrapper -->
<div id="main-content" style="opacity: 0;">

  <!-- Header with Navigation -->
  <header class="bg-dark/95 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-4 py-3">
      <nav class="flex items-center justify-between">
        <div class="flex items-center">
          <a href="/">
            <img src="images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
          </a>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
          <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
          <a href="news.html" class="text-primary font-medium hover:text-white transition">News</a>
          <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
          <a href="resources.html" class="text-white font-medium hover:text-primary transition">Resources</a>
          <a href="contact-form.html" class="text-white font-medium hover:text-primary transition">Contact</a>
          <a href="registration-form.html" class="border border-primary px-6 py-2 text-white font-medium hover:text-primary transition flex items-center justify-center">Register Now</a>
        </div>

        <!-- Mobile Navigation Toggle -->
        <div class="md:hidden">
          <button id="mobile-menu-button" class="text-white hover:text-primary">
            <i class="fas fa-bars text-xl"></i>
          </button>
        </div>
      </nav>

      <!-- Mobile Navigation Menu -->
      <div id="mobile-menu" class="md:hidden hidden fixed inset-0 bg-dark bg-opacity-95 z-50">
        <!-- Close button -->
        <button id="mobile-menu-close" class="absolute top-6 right-6 text-white hover:text-primary">
          <i class="fas fa-times text-2xl"></i>
        </button>

        <!-- Menu content -->
        <div class="flex flex-col items-center justify-center h-full w-full">
          <div class="flex flex-col items-center space-y-10 py-8">
            <a href="/" class="text-white text-2xl font-medium hover:text-primary transition">Home</a>
            <a href="about.html" class="text-white text-2xl font-medium hover:text-primary transition">About Us</a>
            <a href="news.html" class="text-primary text-2xl font-medium hover:text-white transition">News</a>
            <a href="events.html" class="text-white text-2xl font-medium hover:text-primary transition">Events</a>
            <a href="resources.html" class="text-white text-2xl font-medium hover:text-primary transition">Resources</a>
            <a href="contact-form.html" class="text-white text-2xl font-medium hover:text-primary transition">Contact</a>
            <a href="registration-form.html" class="text-white text-2xl font-medium hover:text-primary transition">Register Now</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="relative bg-dark/95 text-white pt-32 pb-12">
    <div class="container mx-auto px-4">
      <div class="text-center max-w-4xl mx-auto" data-aos="fade-up">
        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
          News
          <span class="text-primary">Updates</span>
        </h1>
        <p class="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
          Stay connected with the latest news, events, and opportunities from the Khuddam community
        </p>
        <div class="w-24 h-1 bg-primary mx-auto mb-8"></div>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto mt-8" data-aos="fade-up" data-aos-delay="200">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input
                    type="text"
                    id="search-input"
                    placeholder="Search posts by title or description..."
                    class="w-full pl-12 pr-12 py-4 border border-primary focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 text-white placeholder-gray-400 bg-dark/70 shadow-sm rounded-lg"
            >
            <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
              <button id="clear-search" class="text-gray-400 hover:text-gray-300 transition-colors duration-200 hidden">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Search Results Info -->
          <div id="search-results-info" class="mt-4 text-center text-white/70 hidden">
            <span id="results-count"></span>
          </div>

          <!-- No Results Message -->
          <div id="no-results" class="mt-8 text-center text-white/60 hidden">
            <div class="mb-4">
              <i class="fas fa-search text-4xl text-white/30"></i>
            </div>
            <h3 class="text-xl font-medium mb-2 text-white/80">No posts found</h3>
            <p>Try adjusting your search terms or browse all posts below.</p>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- Posts Grid -->
  <section class="py-8 bg-gray-50">
    <div class="container mx-auto px-4">
      <div id="posts-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Posts will be dynamically loaded here -->
      </div>

      <!-- Pagination -->
      <div class="flex justify-center mt-12" id="pagination-container">
        <!-- Pagination buttons will be dynamically generated -->
      </div>
    </div>
  </section>

  <!-- Post Modal -->
  <div id="post-modal" class="fixed inset-0 z-50 hidden opacity-0 transition-opacity duration-300">
    <!-- Modal Backdrop -->
    <div class="absolute inset-0 bg-black/80 modal-backdrop" id="modal-backdrop"></div>

    <!-- Modal Content -->
    <div id="modal-content-wrapper" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 max-h-[90vh] overflow-y-auto transition-all duration-300">
      <div class="bg-white rounded-lg shadow-2xl">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 id="modal-title" class="text-2xl font-bold text-dark flex-1 pr-4"></h3>
          <button id="close-modal" class="text-gray-500 hover:text-gray-700 text-2xl flex-shrink-0">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
          <div id="modal-image-container" class="mb-6">
            <img id="modal-image" src="" alt="" class="w-full h-auto object-contain rounded-lg bg-gray-100">
          </div>

          <div class="flex items-center gap-4 mb-4">
            <span id="modal-date" class="text-gray-500 text-sm"></span>
          </div>

          <div id="modal-meta" class="mb-4 text-sm text-gray-600">
            <!-- Time and location will be added here if available -->
          </div>

          <div id="modal-description" class="text-gray-700 leading-relaxed">
            <!-- Full description will be loaded here -->
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Footer -->
  <footer id="footer" class="bg-dark/95 shadow-md text-white -mt-1">
    <div class="container mx-auto px-4 py-10">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <!-- Logos -->
        <div class="mb-6 md:mb-0 flex items-center space-x-4">
          <div class="flex items-center">
            <div class="mr-2">
              <img src="./images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12">
            </div>
          </div>
          <div class="flex items-center">
            <a href="/">
              <img src="./images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12">
            </a>
          </div>
        </div>

        <!-- Navigation Links -->
        <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
          <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
          <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
          <a href="news.html" class="text-primary font-medium hover:text-white transition">News</a>
          <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
          <a href="resources.html" class="text-white font-medium hover:text-primary transition">Resources</a>
          <a href="registration-form.html" class="text-white font-medium hover:text-primary transition">Register Now</a>
          <a href="contact-form.html" class="text-white font-medium hover:text-primary transition">Contact</a>
        </div>

        <!-- Social Media -->
        <div class="flex space-x-4">
          <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
            <i class="fab fa-facebook-f"></i>
          </a>
          <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
            <i class="fab fa-twitter"></i>
          </a>
          <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
            <i class="fab fa-instagram"></i>
          </a>
          <a href="https://www.youtube.com/@KhuddamUlQuranNZ" target="_blank" class="text-white/70 hover:text-primary transition-all duration-300">
            <i class="fab fa-youtube"></i>
          </a>
        </div>
      </div>

      <div class="border-t border-white/10 mt-8 pt-6">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
          <div class="mt-4 md:mt-0">
            <ul class="flex space-x-6 text-sm">
              <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
              <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
            </ul>
          </div>
        </div>

        <!-- Developer Credits -->
        <div class="mt-4 pt-4 border-t border-white/5">
          <div class="text-center">
            <p class="text-white/50 text-xs">
              Website developed by
              <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                Kiwiorbit
              </a>
              | Contact:
              <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                +64 22 325 9094
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>

</div> <!-- End Main Content Wrapper -->

<!-- JavaScript -->
<script src="js/main.js" defer></script>

<!-- Community Page JavaScript -->
<script>
  // Initialize AOS
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true
  });



</script>

<!-- News Posts Management System -->
<script src="js/news-posts.js"></script>

<script>
  // Initialize community posts when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    initializeCommunityPosts();
  });
</script>

</div>
</body>
</html>
