<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arabic Grammar Batch 8 Class - Khuddam Gallery</title>
    <meta name="description" content="Photos of students from Batch 8 studying Arabic Grammar in their class under the guidance of <PERSON>tad <PERSON>.">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- External CSS resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['<PERSON><PERSON>', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <style>
        /* Infinite scroll gallery styles */
        .photo-item {
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(20px);
        }

        .photo-item.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        .photo-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        /* Modal styles */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.9);
        }

        #modal-image {
            max-height: 90vh;
            max-width: 90vw;
            object-fit: contain;
        }

        /* Loading spinner */
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #deae35;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* CSS Grid layout: 1 → 2 → 3 → 4 photos per row */
        .photos-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }

        @media (min-width: 640px) {
            .photos-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }
        }

        @media (min-width: 768px) {
            .photos-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.75rem;
            }
        }

        @media (min-width: 1024px) {
            .photos-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 1rem;
            }
        }

        .photo-item {
            width: 100%;
            aspect-ratio: 4/3;
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>

<body class="bg-gray-50 font-sans text-dark">
    <!-- Header with Navigation -->
    <header class="bg-dark/95 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="/">
                        <img src="../../images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="/about/" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="/news/" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="/events/" class="text-primary font-medium hover:text-white transition">Events</a>
                    <a href="/contact_form/" class="text-white font-medium hover:text-primary transition">Contact</a>
                    <a href="/registration_form/" class="border border-primary px-6 py-2 text-white font-medium hover:text-primary transition flex items-center justify-center">Register Now</a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden text-white hover:text-primary transition">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- Event Header -->
    <section class="relative bg-dark/95 text-white pt-32 pb-16">
        <div class="container mx-auto px-4">
            <!-- Back Button -->
            <div class="mb-6">
                <a href="/events/" class="inline-flex items-center text-white/80 hover:text-primary transition">
                    <i class="fas fa-arrow-left mr-2"></i>
                    <span>Back to Events</span>
                </a>
            </div>

            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                    Arabic Grammar Batch 8
                    <span class="text-primary">Class</span>
                </h1>
                <div class="flex flex-wrap justify-center items-center gap-6 text-white/80 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-calendar-alt mr-2 text-primary"></i>
                        <span id="event-date">December 15, 2024</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-2 text-primary"></i>
                        <span>NZICT, Mount Roskill</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-images mr-2 text-primary"></i>
                        <span id="photo-count">24 Photos</span>
                    </div>
                </div>
                <p class="text-xl text-white/80 max-w-2xl mx-auto">
                    Photos of students from Batch 8 studying Arabic Grammar in their class under the guidance of Ustad Mamoon Saeed.
                </p>
            </div>
        </div>
    </section>

    <!-- Photos Gallery -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 lg:px-6 xl:px-8">
            <div id="photos-container" class="photos-grid">
                <!-- Photos will be dynamically loaded here -->
            </div>

            <!-- Loading indicator -->
            <div id="loading-indicator" class="flex justify-center items-center py-8 hidden">
                <div class="loading-spinner mr-4"></div>
                <span class="text-secondary">Loading more photos...</span>
            </div>

            <!-- End of photos indicator -->
            <div id="end-indicator" class="text-center py-8 hidden">
                <p class="text-secondary">You've reached the end of the gallery</p>
                <a href="/events/" class="inline-flex items-center mt-4 text-primary hover:text-dark transition">
                    <i class="fas fa-arrow-left mr-2"></i>
                    <span>Back to All Events</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Image Modal -->
    <div id="image-modal" class="fixed inset-0 z-50 hidden opacity-0 transition-opacity duration-300">
        <!-- Modal Backdrop -->
        <div class="absolute inset-0 modal-backdrop" id="modal-backdrop"></div>

        <!-- Modal Content -->
        <div class="relative z-10 flex items-center justify-center min-h-screen p-4">
            <div class="relative">
                <!-- Close Button -->
                <button id="close-modal" class="absolute -top-12 right-0 text-white hover:text-primary transition-colors z-20">
                    <i class="fas fa-times text-3xl"></i>
                </button>

                <!-- Navigation Arrows -->
                <button id="prev-photo" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-primary transition-colors z-20">
                    <i class="fas fa-chevron-left text-3xl"></i>
                </button>
                <button id="next-photo" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-primary transition-colors z-20">
                    <i class="fas fa-chevron-right text-3xl"></i>
                </button>

                <!-- Image -->
                <img id="modal-image" src="" alt="" class="rounded-lg shadow-2xl">

                <!-- Image Counter -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black/50 px-4 py-2 rounded-lg">
                    <span id="image-counter">1 of 24</span>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../../js/main.js"></script>
    <script src="../../js/events-posts.js"></script>
    <script>
        // Event-specific data
        const eventId = 1; // Arabic Grammar Batch 8 Graduation
        let currentEvent = null;
        let loadedPhotos = [];
        let photosPerLoad = 6;
        let currentPhotoIndex = 0;
        let isLoading = false;
        let allPhotosLoaded = false;

        // DOM elements
        let photosContainer, loadingIndicator, endIndicator;
        let imageModal, modalImage, modalBackdrop, closeModalBtn, prevPhotoBtn, nextPhotoBtn, imageCounter;

        // Initialize gallery
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventGallery();
        });

        function initializeEventGallery() {
            // Get DOM elements
            photosContainer = document.getElementById('photos-container');
            loadingIndicator = document.getElementById('loading-indicator');
            endIndicator = document.getElementById('end-indicator');

            // Get modal elements
            imageModal = document.getElementById('image-modal');
            modalImage = document.getElementById('modal-image');
            modalBackdrop = document.getElementById('modal-backdrop');
            closeModalBtn = document.getElementById('close-modal');
            prevPhotoBtn = document.getElementById('prev-photo');
            nextPhotoBtn = document.getElementById('next-photo');
            imageCounter = document.getElementById('image-counter');

            if (!photosContainer) {
                console.error('Photos container not found');
                return;
            }

            // Find current event
            if (typeof events !== 'undefined') {
                currentEvent = events.find(event => event.id === eventId);
                if (currentEvent) {
                    loadedPhotos = [...currentEvent.photos];

                    // Update dynamic content
                    updateEventInfo();

                    setupInfiniteScroll();
                    loadInitialPhotos();
                    setupModal();
                }
            }
        }

        function setupInfiniteScroll() {
            // Intersection Observer for infinite scroll
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !isLoading && !allPhotosLoaded) {
                        loadMorePhotos();
                    }
                });
            }, {
                rootMargin: '100px'
            });

            // Observe the loading indicator
            if (loadingIndicator) {
                observer.observe(loadingIndicator);
            }
        }

        function loadInitialPhotos() {
            // Load all photos at once for better user experience
            loadAllPhotos();
        }

        function loadAllPhotos() {
            if (isLoading) return;

            isLoading = true;
            loadingIndicator.classList.remove('hidden');

            // Load all photos with staggered animation
            setTimeout(() => {
                loadedPhotos.forEach((photo, index) => {
                    setTimeout(() => {
                        const photoElement = createPhotoElement(photo, index);
                        photosContainer.appendChild(photoElement);

                        // Trigger animation
                        setTimeout(() => {
                            photoElement.classList.add('loaded');
                        }, 50);
                    }, index * 50); // Faster animation for all photos
                });

                allPhotosLoaded = true;
                loadingIndicator.classList.add('hidden');
                endIndicator.classList.remove('hidden');
                isLoading = false;
            }, 300);
        }

        function loadMorePhotos() {
            if (isLoading || allPhotosLoaded) return;

            isLoading = true;
            loadingIndicator.classList.remove('hidden');

            // Simulate loading delay for smooth experience
            setTimeout(() => {
                const startIndex = currentPhotoIndex;
                const endIndex = Math.min(startIndex + photosPerLoad, loadedPhotos.length);
                const photosToLoad = loadedPhotos.slice(startIndex, endIndex);

                photosToLoad.forEach((photo, index) => {
                    setTimeout(() => {
                        const photoElement = createPhotoElement(photo, startIndex + index);
                        photosContainer.appendChild(photoElement);

                        // Trigger animation
                        setTimeout(() => {
                            photoElement.classList.add('loaded');
                        }, 50);
                    }, index * 100);
                });

                currentPhotoIndex = endIndex;

                if (currentPhotoIndex >= loadedPhotos.length) {
                    allPhotosLoaded = true;
                    loadingIndicator.classList.add('hidden');
                    endIndicator.classList.remove('hidden');
                } else {
                    loadingIndicator.classList.add('hidden');
                }

                isLoading = false;
            }, 500);
        }

        function createPhotoElement(photo, index) {
            const photoDiv = document.createElement('div');
            photoDiv.className = 'photo-item bg-white rounded-lg overflow-hidden shadow-lg cursor-pointer';
            photoDiv.setAttribute('data-index', index);

            photoDiv.innerHTML = `
                <img src="${photo.image}" alt="" loading="lazy">
            `;

            // Add click event to open modal
            photoDiv.addEventListener('click', () => openModal(index));

            return photoDiv;
        }

        function setupModal() {
            if (!imageModal) return;

            // Event listeners
            closeModalBtn.addEventListener('click', closeModal);
            modalBackdrop.addEventListener('click', closeModal);
            prevPhotoBtn.addEventListener('click', showPreviousPhoto);
            nextPhotoBtn.addEventListener('click', showNextPhoto);

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (!imageModal.classList.contains('hidden')) {
                    switch(e.key) {
                        case 'Escape':
                            closeModal();
                            break;
                        case 'ArrowLeft':
                            showPreviousPhoto();
                            break;
                        case 'ArrowRight':
                            showNextPhoto();
                            break;
                    }
                }
            });
        }

        function openModal(photoIndex) {
            if (!currentEvent || !imageModal) return;

            const photo = loadedPhotos[photoIndex];
            if (!photo) return;

            modalImage.src = photo.image;
            modalImage.alt = photo.alt;
            updateImageCounter(photoIndex);

            imageModal.classList.remove('hidden');
            imageModal.classList.remove('opacity-0');
            document.body.style.overflow = 'hidden';

            // Store current photo index for navigation
            window.currentModalPhotoIndex = photoIndex;
        }

        function closeModal() {
            if (imageModal) {
                imageModal.classList.add('opacity-0');
                setTimeout(() => {
                    imageModal.classList.add('hidden');
                }, 300);
                document.body.style.overflow = '';
            }
        }

        function showPreviousPhoto() {
            if (window.currentModalPhotoIndex > 0) {
                window.currentModalPhotoIndex--;
                const photo = loadedPhotos[window.currentModalPhotoIndex];
                modalImage.src = photo.image;
                modalImage.alt = photo.alt;
                updateImageCounter(window.currentModalPhotoIndex);
            }
        }

        function showNextPhoto() {
            if (window.currentModalPhotoIndex < loadedPhotos.length - 1) {
                window.currentModalPhotoIndex++;
                const photo = loadedPhotos[window.currentModalPhotoIndex];
                modalImage.src = photo.image;
                modalImage.alt = photo.alt;
                updateImageCounter(window.currentModalPhotoIndex);
            }
        }

        function updateImageCounter(index) {
            if (imageCounter) {
                imageCounter.textContent = `${index + 1} of ${loadedPhotos.length}`;
            }
        }

        function updateEventInfo() {
            if (!currentEvent) return;

            // Update photo count
            const photoCountElement = document.getElementById('photo-count');
            if (photoCountElement) {
                photoCountElement.textContent = `${currentEvent.photoCount} Photos`;
            }

            // Update event date
            const eventDateElement = document.getElementById('event-date');
            if (eventDateElement) {
                const date = new Date(currentEvent.date);
                const formattedDate = date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                eventDateElement.textContent = formattedDate;
            }
        }
    </script>
</body>
</html>
