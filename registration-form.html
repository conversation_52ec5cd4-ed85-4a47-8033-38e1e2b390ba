<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Khuddam | Quranic Arabic Grammar Learning Programme</title>
    <meta name="description" content="Register for our Quranic Arabic Grammar Learning Programme. Free classes for male adults starting February/March 2025. Enhance your connection to the Quran through linguistic tools.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://khuddam.co.nz/registration-form.html">
    <meta property="og:title" content="Register for Khuddam's Quranic Arabic Grammar Learning Programme">
    <meta property="og:description" content="Register now for our free Quranic Arabic Grammar classes for male adults starting February/March 2025. Secure your place today!">
    <meta property="og:image" content="images/enrollment-post.png">
    <meta property="og:image:alt" content="Khuddam Quranic Arabic Grammar Learning Programme">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://khuddam.co.nz/registration-form.html">
    <meta name="twitter:title" content="Register for Khuddam's Quranic Arabic Grammar Learning Programme">
    <meta name="twitter:description" content="Register now for our free Quranic Arabic Grammar classes for male adults starting February/March 2025. Secure your place today!">
    <meta name="twitter:image" content="images/enrollment-post.png">
    <meta name="twitter:image:alt" content="Khuddam Quranic Arabic Grammar Learning Programme">

    <!-- Critical CSS inlined for faster rendering -->
    <style>
/* Critical CSS for above-the-fold content */

/* Basic body styling */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #101010;
    margin: 0;
    padding: 0;
}

/* Arabic text styling */
.font-arabic, .arabic-text {
    font-family: 'Amiri', serif;
    direction: rtl;
}

/* Main content styles */
#main-content {
    opacity: 1;
    visibility: visible;
}

/* Arabic text styling */
.arabic-text {
    text-align: center;
    font-size: 28px;
    line-height: 1.8;
    color: #deae35;
    margin: 20px 0 30px;
    opacity: 0.9;
    letter-spacing: 1px;
}


/* Header styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    transition: all 0.3s;
    background-color: rgba(16, 16, 16, 0.95);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile menu styles */
#mobile-menu {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

#mobile-menu.hidden {
    display: none;
}

#mobile-menu:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

/* Color variables */
:root {
    --primary: #deae35;
    --dark: #101010;
}

/* Utility classes used in above-the-fold content */
.container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.text-primary {
    color: #deae35;
}

.text-white {
    color: #ffffff;
}

.bg-dark\/95 {
    background-color: rgba(16, 16, 16, 0.95);
}

.border-primary {
    border-color: #deae35;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive adjustments */
@media (min-width: 768px) {
    .md\:hidden {
        display: none;
    }

    .md\:flex {
        display: flex;
    }
}

@media (max-width: 767px) {
    .hidden.md\:flex {
        display: none;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* For Safari and older browsers that don't support scroll-behavior */
body {
    -webkit-overflow-scrolling: touch;
}
    </style>

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Preload critical icons -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

    <!-- Google Fonts - full set loaded asynchronously -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;600;700&display=swap"></noscript>

    <!-- Custom CSS loaded asynchronously -->
    <link rel="stylesheet" href="css/critical.css">

    <!-- Helper function for CSS loading -->
    <script>
        /* Fallback for browsers that don't support preload */
        !function(n){"use strict";n.loadCSS||(n.loadCSS=function(){});var t=loadCSS.relpreload={};if(t.support=function(){var e;try{e=n.document.createElement("link").relList.supports("preload")}catch(t){e=!1}return function(){return e}}(),t.bindMediaToggle=function(t){var e=t.media||"all";function a(){t.addEventListener?t.removeEventListener("load",a):t.attachEvent&&t.detachEvent("onload",a),t.setAttribute("onload",null),t.media=e}t.addEventListener?t.addEventListener("load",a):t.attachEvent&&t.attachEvent("onload",a),setTimeout(function(){t.rel="stylesheet",t.media="only x"}),setTimeout(a,3e3)},t.poly=function(){if(!t.support())for(var e=n.document.getElementsByTagName("link"),a=0;a<e.length;a++){var o=e[a];"preload"!==o.rel||"style"!==o.getAttribute("as")||o.getAttribute("data-loadcss")||(o.setAttribute("data-loadcss",!0),t.bindMediaToggle(o))}},!t.support()){t.poly();var e=n.setInterval(t.poly,500);n.addEventListener?n.addEventListener("load",function(){t.poly(),n.clearInterval(e)}):n.attachEvent&&n.attachEvent("onload",function(){t.poly(),n.clearInterval(e)})}"undefined"!=typeof exports?exports.loadCSS=loadCSS:n.loadCSS=loadCSS}("undefined"!=typeof global?global:this);
    </script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        arabic: ['Amiri', 'serif'],
                        sans: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebPage",
                "@id": "https://khuddam.co.nz/registration-form.html#webpage",
                "url": "https://khuddam.co.nz/registration-form.html",
                "name": "Register - Khuddam | Quranic Arabic Grammar Learning Programme",
                "description": "Register for our Quranic Arabic Grammar Learning Programme. Free classes for male adults starting February/March 2025.",
                "isPartOf": {
                    "@id": "https://khuddam.co.nz/#website"
                },
                "inLanguage": "en",
                "breadcrumb": {
                    "@id": "https://khuddam.co.nz/registration-form.html#breadcrumblist"
                },
                "about": {
                    "@id": "https://khuddam.co.nz/#organization"
                }
            },
            {
                "@type": "BreadcrumbList",
                "@id": "https://khuddam.co.nz/registration-form.html#breadcrumblist",
                "itemListElement": [
                    {
                        "@type": "ListItem",
                        "position": 1,
                        "item": {
                            "@id": "https://khuddam.co.nz/",
                            "name": "Home"
                        }
                    },
                    {
                        "@type": "ListItem",
                        "position": 2,
                        "item": {
                            "@id": "https://khuddam.co.nz/registration-form.html",
                            "name": "Register"
                        }
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://khuddam.co.nz/#organization",
                "name": "Khuddam Ul Quran",
                "url": "https://khuddam.co.nz/"
            }
        ]
    }
    </script>
</head>
<body class="bg-black font-sans text-dark">
    <div id="main-content">
    <!-- Header with Navigation -->
    <header class="bg-dark/90 shadow-md fixed top-0 left-0 right-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4 py-3">
            <nav class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="/">
                        <img src="images/old-logo-border.png" alt="Khuddam - Servants of the Quran" class="h-12">
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="news.html" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="resources.html" class="text-white font-medium hover:text-primary transition">Resources</a>
                    <a href="contact-form.html" class="text-white font-medium hover:text-primary transition">Contact</a>
                    <a href="registration-form.html" class="border border-primary px-6 py-2 text-primary font-medium hover:text-white transition flex items-center justify-center">Register Now</a>
                </div>

                <!-- Mobile Navigation Toggle -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-white hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </nav>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="md:hidden hidden fixed inset-0 bg-dark bg-opacity-95 z-50">
                <!-- Close button -->
                <button id="mobile-menu-close" class="absolute top-6 right-6 text-white hover:text-primary">
                    <i class="fas fa-times text-2xl"></i>
                </button>

                <!-- Menu content -->
                <div class="flex flex-col items-center justify-center h-full w-full">
                    <div class="flex flex-col items-center space-y-10 py-8">
                        <a href="/" class="text-white text-2xl font-medium hover:text-primary transition">Home</a>
                        <a href="about.html" class="text-white text-2xl font-medium hover:text-primary transition">About Us</a>
                        <a href="news.html" class="text-white text-2xl font-medium hover:text-primary transition">News</a>
                        <a href="events.html" class="text-white text-2xl font-medium hover:text-primary transition">Events</a>
                        <a href="resources.html" class="text-white text-2xl font-medium hover:text-primary transition">Resources</a>
                        <a href="contact-form.html" class="text-white text-2xl font-medium hover:text-primary transition">Contact</a>
                        <a href="registration-form.html" class="text-primary text-2xl font-medium hover:text-white transition">Register Now</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title Section enable when registation is open -->
    <!-- <section id="page-title" class="relative pt-32 pb-16 bg-black/50">
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-4">Register Now</h1>
                <div class="w-32 h-1 bg-primary mx-auto mb-8"></div>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    Join our Quranic Arabic Grammar Learning Programme and enhance your connection to the Quran.
                </p>
            </div>
        </div>
    </section> -->

    <!-- Page Title Section enable when registation is closed -->
    <section id="page-title" class="relative pt-32 pb-16 bg-black/50">
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-4">Registration <span style="color: var(--primary);">Closed!</span></h1>
                <div class="w-32 h-1 bg-primary mx-auto mb-8"></div>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    <span style="font-weight: bold;">Registration for Arabic Batch-10 is now closed.</span> <br>
                    We sincerely appreciate your interest in our program.
                </p>
            </div>
        </div>
    </section>

    <!-- Waiting List Section -->
    <section class="py-16 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 w-full h-full">
            <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent"></div>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <!-- Icon -->
                <div class="mb-8 flex justify-center">
                    <div class="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center shadow-lg">
                        <i class="fas fa-bell text-primary text-3xl"></i>
                    </div>
                </div>

                <!-- Heading -->
                <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                    Want to be the <span class="text-primary">first to know</span> when registration opens?
                </h2>

                <!-- Description -->
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                    Join our waiting list and we'll notify you as soon as the next batch is announced.
                    Be among the first to secure your spot in our upcoming Quranic Arabic Grammar programme.
                </p>

                <!-- Join Waiting List Button -->
                <div class="mb-12">
                    <a href="https://docs.google.com/forms/d/e/1FAIpQLSeJ_pwMI9MZgc4j91BQa_uDG7fgsIiKRH_OOJjsLwgkCTpmJQ/viewform?usp=header"
                       target="_blank"
                       class="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-black font-bold text-lg px-10 py-4 rounded-sm transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group">
                        <i class="fas fa-user-plus mr-3 text-xl group-hover:scale-110 transition-transform"></i>
                        Join Waiting List
                        <i class="fas fa-external-link-alt ml-3 text-sm opacity-70"></i>
                    </a>
                </div>

                <!-- Contact Information -->
                <div class="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-lg p-8 max-w-2xl mx-auto shadow-lg">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center justify-center">
                        <i class="fas fa-question-circle text-primary mr-3"></i>
                        Have Questions?
                    </h3>

                    <p class="text-gray-600 mb-6">
                        Feel free to reach out to us for any inquiries about the programme:
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Phone Contact -->
                        <div class="flex items-center justify-center">
                            <div class="flex items-center px-4 py-3">
                                <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                                    <i class="fas fa-phone text-primary"></i>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-500 text-sm">Call us</p>
                                    <a href="tel:+6422161557" class="text-gray-800 font-medium hover:text-primary transition">
                                        +64 22 161 5574
                                    </a>
                                    <p class="text-gray-400 text-xs">Mon-Fri, 6-10 PM</p>
                                </div>
                            </div>
                        </div>

                        <!-- WhatsApp Contact -->
                        <!-- <div class="flex items-center justify-center">
                            <div class="flex items-center px-4 py-3">
                                <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                                    <i class="fab fa-mail text-primary"></i>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-500 text-sm">Email us</p>
                                    <a href="<EMAIL>" target="_blank" class="text-gray-800 font-medium hover:text-[#25D366] transition">
                                        <EMAIL>
                                    </a>
                                    <p class="text-gray-400 text-xs">Click here to message</p>
                                </div>
                            </div>
                        </div> -->

                        <!-- Email Contact -->
                        <div class="flex items-center justify-center pl-12 lg:pl-0">
                            <div class="flex items-center px-4 py-3">
                                <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                                    <i class="fas fa-envelope text-primary"></i>
                                </div>
                                <div class="text-left">
                                    <p class="text-gray-500 text-sm">Email us</p>
                                    <a href="mailto:<EMAIL>" class="text-gray-800 font-medium hover:text-primary transition">
                                        <EMAIL>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Registration Form Section -->
    <section class="py-20 bg-dark -mt-1 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 w-full h-full">
            <!-- Masjid Interior Background Image -->
            <img src="./images/Masjid Interior.webp" alt="" class="absolute inset-0 w-full h-full object-cover opacity-20">
        </div>

        <!-- Overlay that covers the registration form to disable it --> 
        <div id="formOverlay" class="absolute inset-0 backdrop-blur-sm flex flex-col items-center justify-center text-white z-50 rounded-md">
            <h3 class="text-3xl font-bold mb-4">Registration Closed</h3>
            <div class="mb-12">
                <a href="https://docs.google.com/forms/d/e/1FAIpQLSeJ_pwMI9MZgc4j91BQa_uDG7fgsIiKRH_OOJjsLwgkCTpmJQ/viewform?usp=header"
                    target="_blank"
                    class="inline-flex items-center justify-center bg-primary hover:bg-primary/90 text-black font-bold text-lg px-10 py-4 rounded-sm transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group">
                    <i class="fas fa-user-plus mr-3 text-xl group-hover:scale-110 transition-transform"></i>
                    Join the Waiting List
                    <i class="fas fa-external-link-alt ml-3 text-sm opacity-70"></i>
                </a>
            </div>
        </div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-5xl mx-auto">
                <!-- Form Card -->
                <div id="form-card" class="bg-dark hover:bg-dark/30 hover:backdrop-blur-md transition-all duration-500 border border-primary/30 shadow-2xl rounded-sm overflow-hidden">
                    <!-- Header -->
                    <div class="bg-black/50 p-4 border-b border-white/10">
                        <div class="flex justify-end items-center">
                            <div class="text-white/70 text-sm flex items-center">
                                <i class="fas fa-clock text-primary mr-2"></i>
                                <span>Takes about 2 minutes</span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Content -->
                    <div id="form-content" class="p-8 md:p-12">
                        <div class="text-center mb-10">

                            <h2 class="text-4xl md:text-5xl font-bold text-white mb-4 text-center">
                                <span id="form-title" class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">Register in 2 Steps</span>
                            </h2>
                            <p class="text-xl text-white/80 mb-2">Join our Arabic Grammar Class now.</p>
                        </div>

                        <!-- Multi-step Form -->
                        <form id="registrationForm" class="max-w-2xl mx-auto">
                            <!-- Step Indicator -->
                            <div class="mb-8">
                                <div class="flex items-center justify-center space-x-4">
                                    <div class="flex items-center">
                                        <div id="step1-indicator" class="w-8 h-8 rounded-full bg-primary text-black flex items-center justify-center font-bold text-sm">
                                            1
                                        </div>
                                        <span id="step1-text" class="ml-2 text-white font-medium">Registration Details</span>
                                    </div>
                                    <div class="w-12 h-0.5 bg-white/20" id="step-connector"></div>
                                    <div class="flex items-center">
                                        <div id="step2-indicator" class="w-8 h-8 rounded-full bg-white/20 text-white/60 flex items-center justify-center font-bold text-sm">
                                            2
                                        </div>
                                        <span id="step2-text" class="ml-2 text-white/60 font-medium">Join WhatsApp Group</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 1: Registration Details -->
                            <div id="step1" class="step-content">
                                <!-- Personal Information Section -->
                                <div class="mb-8">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="fullName" class="block text-white font-medium mb-2 transition-all duration-300">
                                            <i class="fas fa-user text-primary mr-2"></i>Full Name
                                        </label>
                                        <div class="relative">
                                            <input
                                                type="text"
                                                id="fullName"
                                                name="fullName"
                                                placeholder="Zaid Hamid"
                                                class="w-full bg-white/5 border border-white/10 text-white px-4 py-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                                required
                                            />
                                            <div class="input-status-icon hidden absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="email" class="block text-white font-medium mb-2 transition-all duration-300">
                                            <i class="fas fa-envelope text-primary mr-2"></i>Email Address
                                        </label>
                                        <div class="relative">
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                placeholder="<EMAIL>"
                                                class="w-full bg-white/5 border border-white/10 text-white px-4 py-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                                required
                                            />
                                            <div class="input-status-icon hidden absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 form-group">
                                    <label for="phone" class="block text-white font-medium mb-2 transition-all duration-300">
                                        <i class="fas fa-phone text-primary mr-2"></i>Phone Number (WhatsApp)
                                    </label>
                                    <div class="relative">
                                        <div class="flex">
                                            <div class="bg-black/40 text-white px-3 py-3 rounded-l-sm flex items-center justify-center border border-white/10 border-r-0">
                                                +64
                                            </div>
                                            <input
                                                type="tel"
                                                id="phone"
                                                name="phone"
                                                placeholder="2757 93314"
                                                class="w-full bg-white/5 border border-white/10 text-white px-4 py-3 rounded-r-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                                required
                                            />
                                        </div>
                                        <div class="input-status-icon hidden absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Learning Preferences Section -->
                            <div class="mb-8">
                                <div class="form-group mb-6">
                                    <label for="motivation" class="block text-white font-medium mb-2 transition-all duration-300">
                                        <i class="fas fa-heart text-primary mr-2"></i>What is your motivation to learn Quranic Arabic Grammar?
                                    </label>
                                    <div class="relative">
                                        <textarea
                                            id="motivation"
                                            name="motivation"
                                            placeholder="Please share your motivation to learn Quranic Arabic Grammar"
                                            rows="5"
                                            class="w-full bg-white/5 border border-white/10 text-white px-4 py-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                            required
                                        ></textarea>
                                    </div>
                                </div>
                            </div>
                            <!-- Additional Information Section -->
                            <div class="mb-8">
                                <div class="form-group mb-2">
                                    <label for="comments" class="block text-white font-medium mb-2 transition-all duration-300">
                                        <i class="fas fa-comment text-primary mr-2"></i>Additional Comments (Optional)
                                    </label>
                                    <div class="relative">
                                        <textarea
                                            id="comments"
                                            name="comments"
                                            placeholder="Any additional information you'd like to share"
                                            rows="4"
                                            class="w-full bg-white/5 border border-white/10 text-white px-4 py-3 rounded-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                                        ></textarea>
                                    </div>
                                </div>



                                <div class="mb-8 p-4">
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5 mt-1">
                                            <input
                                                type="checkbox"
                                                id="whatsappConsent"
                                                name="whatsappConsent"
                                                class="w-4 h-4 accent-primary cursor-pointer"
                                                required
                                            />
                                        </div>
                                        <div class="ml-3">
                                            <label for="whatsappConsent" class="text-white text-sm font-medium">
                                                I agree to join the WhatsApp group for class updates and notifications.
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                </div>
                                <!-- Step 1 Submit Button -->
                                <button
                                    type="button"
                                    id="step1SubmitBtn"
                                    class="w-full bg-primary hover:bg-primary/90 text-black font-bold px-8 py-3 rounded-sm transition-all duration-300 flex items-center justify-center group text-md"
                                >
                                    <span>Step 2 : Join WhatsApp Group</span>
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </button>
                            </div>

                            <!-- Step 2: Join WhatsApp Group -->
                            <div id="step2" class="step-content hidden">
                                <div class="text-center">
                                    <!-- WhatsApp Icon -->
                                    <div class="mb-6 flex justify-center">
                                        <div class="w-20 h-20 rounded-full bg-[#25D366]/20 flex items-center justify-center">
                                            <i class="fab fa-whatsapp text-[#25D366] text-4xl"></i>
                                        </div>
                                    </div>

                                    <!-- Message -->
                                    <h3 class="text-2xl font-bold text-white mb-4">CLick the button below to Join! 👇</h3>
                                    <p class="text-white/90 text-base mb-6">
                                        To complete your registration and stay updated with class schedules and materials, please join our WhatsApp group by clicking the button below or scanning this QR code.
                                    </p>

                                    <!-- QR Code -->
                                    <div class="mb-6 flex justify-center">
                                        <div class="p-4">
                                            <img src="./images/barcode.png" alt="QR code to join Khuddam WhatsApp Group for program updates" class="w-40 md:w-48">
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="space-y-4">
                                        <!-- WhatsApp Join Button -->
                                        <a href="https://chat.whatsapp.com/GqCM92Qp90cJA1ZDZHGmNH" target="_blank" id="joinWhatsAppButton" class="w-full bg-[#25D366] hover:bg-[#25D366]/90 text-white font-bold text-base px-8 py-4 inline-flex items-center justify-center transition-all duration-300 shadow-lg rounded-sm">
                                            <i class="fab fa-whatsapp text-md mr-2"></i>
                                            Join Arabic Class WhatsApp Group
                                        </a>


                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Thank You Modal -->
    <div id="thankYouModal" class="fixed inset-0 z-[100] hidden opacity-0 transition-opacity duration-300">
        <!-- Modal Backdrop -->
        <div id="thankYouModalBackdrop" class="absolute inset-0 bg-black/80 backdrop-blur-[2px]"></div>

        <!-- Modal Content -->
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[95%] sm:w-[90%] md:w-[80%] max-w-md opacity-0 translate-y-8 transition-all duration-300 ease-out" id="thankYouModalContent">
            <!-- Main Modal Container -->
            <div class="bg-dark border-2 border-primary overflow-hidden shadow-2xl">
                <!-- Close Button -->
                <button id="closeThankYouModal" class="absolute top-2 right-2 sm:top-4 sm:right-4 text-white hover:text-primary z-20 bg-black/50 w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>

                <!-- Top Banner -->
                <div class="bg-primary py-3 px-6 text-center relative">
                    <h2 class="text-black text-xl font-bold uppercase tracking-wider">Registration Complete!</h2>
                </div>

                <!-- Main Content Area -->
                <div class="p-6 md:p-8 text-center">
                    <!-- Success Icon -->
                    <div class="mb-6 flex justify-center">
                        <div class="w-20 h-20 rounded-full bg-green-500/20 flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-500 text-4xl"></i>
                        </div>
                    </div>

                    <!-- Message -->
                    <h3 class="text-2xl font-bold text-white mb-4">Thank You!</h3>
                    <p class="text-white/90 text-base mb-6">
                        Your registration has been successfully completed. We're excited to have you join our Quranic Arabic Grammar Learning Programme!
                    </p>
                    <p class="text-white/80 text-sm mb-6">
                        You will receive updates and class information through WhatsApp and email. We look forward to seeing you in class!
                    </p>

                    <!-- Home Button -->
                    <button id="thankYouCloseBtn" class="bg-primary hover:bg-primary/90 text-black font-bold px-8 py-3 rounded-sm transition-all duration-300 flex items-center justify-center mx-auto">
                        <i class="fas fa-home mr-2"></i>
                        Go to Home
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark/95 shadow-md text-white -mt-1">
        <div class="container mx-auto px-4 py-10">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- Logos -->
                <div class="mb-6 md:mb-0 flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="mr-2">
                            <img src="images/old-logo-border.png" alt="Khuddam Original Logo" class="h-12">
                        </div>
                    </div>
                    <div class="flex items-center">
                        <a href="/">
                            <img src="images/khuddam-logo-white.png" alt="Khuddam - Servants of the Quran" class="h-12">
                        </a>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex flex-wrap justify-center gap-8 mb-6 md:mb-0">
                    <a href="/" class="text-white font-medium hover:text-primary transition">Home</a>
                    <a href="about.html" class="text-white font-medium hover:text-primary transition">About Us</a>
                    <a href="news.html" class="text-white font-medium hover:text-primary transition">News</a>
                    <a href="events.html" class="text-white font-medium hover:text-primary transition">Events</a>
                    <a href="resources.html" class="text-white font-medium hover:text-primary transition">Resources</a>
                    <a href="registration-form.html" class="text-primary font-medium hover:text-white transition">Register Now</a>
                    <a href="contact-form.html" class="text-white font-medium hover:text-primary transition">Contact</a>
                </div>

                <!-- Social Media -->
                <div class="flex space-x-4">
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://www.youtube.com/@KhuddamUlQuranNZ" target="_blank" class="text-white/70 hover:text-primary transition-all duration-300">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-white/10 mt-8 pt-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-white/70 text-sm text-center w-full md:w-auto">&copy; 2025 Khuddam - Servants of the Quran. All rights reserved.</p>
                    <div class="mt-4 md:mt-0">
                        <ul class="flex space-x-6 text-sm">
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Privacy</a></li>
                            <li><a href="#" class="text-white/70 hover:text-primary transition">Terms</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Developer Credits -->
                <div class="mt-4 pt-4 border-t border-white/5">
                    <div class="text-center">
                        <p class="text-white/50 text-xs">
                            Website developed by
                            <a href="https://kiwiorbit.vercel.app/" target="_blank" class="text-primary hover:text-primary/80 transition-colors font-medium">
                                Kiwiorbit
                            </a>
                            | Contact:
                            <a href="tel:+64223259094" class="text-primary hover:text-primary/80 transition-colors">
                                +64 22 325 9094
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js" defer></script>

    <!-- Basic Smooth Scroll -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Basic smooth scroll implementation for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return; // Skip if it's just "#"

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80, // Offset for header
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Smooth scroll for all scrolling
            document.body.style.scrollBehavior = 'smooth';
        });
    </script>

    <!-- Registration Form Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get elements for multi-step form
            const step1SubmitBtn = document.getElementById('step1SubmitBtn');
            const backToStep1Btn = document.getElementById('backToStep1Btn');
            const joinWhatsAppButton = document.getElementById('joinWhatsAppButton');
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step1Indicator = document.getElementById('step1-indicator');
            const step2Indicator = document.getElementById('step2-indicator');
            const step1Text = document.getElementById('step1-text');
            const step2Text = document.getElementById('step2-text');
            const stepConnector = document.getElementById('step-connector');
            const formTitle = document.getElementById('form-title');
            const formInputs = document.querySelectorAll('#registrationForm input, #registrationForm select, #registrationForm textarea');
            const learningStyleOptions = document.querySelectorAll('.learning-style-option');

            // Get thank you modal elements
            const thankYouModal = document.getElementById('thankYouModal');
            const closeThankYouModal = document.getElementById('closeThankYouModal');
            const thankYouCloseBtn = document.getElementById('thankYouCloseBtn');
            const thankYouModalBackdrop = document.getElementById('thankYouModalBackdrop');

            // Function to show step 2
            function showStep2() {
                // Hide step 1
                step1.classList.add('hidden');

                // Show step 2
                step2.classList.remove('hidden');

                // Update step indicators
                step1Indicator.classList.remove('bg-primary', 'text-black');
                step1Indicator.classList.add('bg-green-500', 'text-white');
                step1Indicator.innerHTML = '<i class="fas fa-check text-sm"></i>';
                step1Text.classList.add('text-green-400');

                step2Indicator.classList.remove('bg-white/20', 'text-white/60');
                step2Indicator.classList.add('bg-primary', 'text-black');
                step2Text.classList.remove('text-white/60');
                step2Text.classList.add('text-white');

                stepConnector.classList.remove('bg-white/20');
                stepConnector.classList.add('bg-green-500');

                // Update form title for Step 2
                if (formTitle) {
                    formTitle.textContent = 'One Final Step';
                }

                // Scroll to page title section to show complete context
                setTimeout(() => {
                    const pageTitleSection = document.querySelector('#page-title');
                    if (pageTitleSection) {
                        pageTitleSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }, 200);
            }

            // Function to show step 1 (go back)
            function showStep1() {
                // Hide step 2
                step2.classList.add('hidden');

                // Show step 1
                step1.classList.remove('hidden');

                // Update step indicators
                step1Indicator.classList.remove('bg-green-500', 'text-white');
                step1Indicator.classList.add('bg-primary', 'text-black');
                step1Indicator.innerHTML = '1';
                step1Text.classList.remove('text-green-400');

                step2Indicator.classList.remove('bg-primary', 'text-black');
                step2Indicator.classList.add('bg-white/20', 'text-white/60');
                step2Text.classList.remove('text-white');
                step2Text.classList.add('text-white/60');

                stepConnector.classList.remove('bg-green-500');
                stepConnector.classList.add('bg-white/20');

                // Restore original form title for Step 1
                if (formTitle) {
                    formTitle.textContent = 'Register in 2 Steps';
                }
            }

            // Function to show thank you modal
            function showThankYouModal() {
                // First make it visible but still transparent
                thankYouModal.classList.remove('hidden');

                // Force a reflow to ensure the transition works
                void thankYouModal.offsetWidth;

                // Fade in the modal backdrop
                thankYouModal.classList.add('opacity-100');

                // Animate in the modal content
                setTimeout(function() {
                    document.getElementById('thankYouModalContent').classList.remove('opacity-0', 'translate-y-8');
                }, 200);
            }

            // Function to close thank you modal
            function closeThankYouModalFunc() {
                // First animate out the content
                document.getElementById('thankYouModalContent').classList.add('opacity-0', 'translate-y-8');

                // Then fade out the backdrop
                thankYouModal.classList.remove('opacity-100');

                // Finally hide the modal after animations complete
                setTimeout(function() {
                    thankYouModal.classList.add('hidden');
                }, 500);
            }

            // Function to reset entire form
            function resetEntireForm() {
                // Get the form element
                const form = document.getElementById('registrationForm');

                // Reset the form fields
                if (form) {
                    form.reset();

                    // Hide all status icons
                    const statusIcons = document.querySelectorAll('.input-status-icon');
                    statusIcons.forEach(icon => {
                        icon.classList.add('hidden');
                    });

                    // Reset learning style options
                    const learningStyleOptions = document.querySelectorAll('.learning-style-option');
                    learningStyleOptions.forEach(option => {
                        option.classList.remove('bg-white/20', 'border-primary');
                    });

                    // Reset step 1 button
                    if (step1SubmitBtn) {
                        step1SubmitBtn.innerHTML = '<span>Continue to WhatsApp Group</span><i class="fas fa-arrow-right ml-2"></i>';
                        step1SubmitBtn.classList.remove('bg-green-500');
                        step1SubmitBtn.classList.add('bg-primary');
                        step1SubmitBtn.disabled = false;
                    }

                    // Reset WhatsApp button state
                    if (joinWhatsAppButton) {
                        joinWhatsAppButton.innerHTML = '<i class="fab fa-whatsapp text-xl mr-2"></i>Join Arabic Class WhatsApp Group';
                        joinWhatsAppButton.classList.remove('bg-green-500', 'hover:bg-green-600');
                        joinWhatsAppButton.classList.add('bg-[#25D366]', 'hover:bg-[#25D366]/90');
                        joinWhatsAppButton.style.pointerEvents = 'auto';
                    }

                    // Clear localStorage
                    localStorage.removeItem('whatsappClicked');
                    localStorage.removeItem('whatsappClickTime');
                    localStorage.removeItem('thankYouModalShown');

                    // Go back to step 1
                    showStep1();

                    // Scroll back to top of form
                    const formContentSection = document.querySelector('#form-content');
                    if (formContentSection) {
                        formContentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            }



            // Function to reset the form
            function resetForm() {
                // Get the form element
                const form = document.getElementById('registrationForm');

                // Reset the form fields
                if (form) {
                    form.reset();

                    // Reset the submit button
                    const submitButton = document.getElementById('showWhatsAppModal');
                    if (submitButton) {
                        submitButton.innerHTML = '<span>Submit</span><i class="fas fa-arrow-right ml-2"></i>';
                        submitButton.classList.remove('bg-green-500');
                        submitButton.classList.add('bg-primary');
                    }

                    // Hide all status icons
                    const statusIcons = document.querySelectorAll('.input-status-icon');
                    statusIcons.forEach(icon => {
                        icon.classList.add('hidden');
                    });

                    // Reset learning style options
                    const learningStyleOptions = document.querySelectorAll('.learning-style-option');
                    learningStyleOptions.forEach(option => {
                        option.classList.remove('bg-white/20', 'border-primary');
                    });

                    // Scroll back to top of form
                    const formContentSection = document.querySelector('#form-content');
                    if (formContentSection) {
                        formContentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            }

            // Function to validate form fields
            function validateForm() {
                const form = document.querySelector('#registrationForm');
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                // Remove any existing error messages
                const existingErrors = form.querySelectorAll('.error-message');
                existingErrors.forEach(error => error.remove());

                // Check each required field
                requiredFields.forEach(field => {
                    field.classList.remove('border-red-500');

                    // Get the parent form-group
                    const formGroup = field.closest('.form-group');
                    if (formGroup) {
                        formGroup.classList.remove('shake-animation');
                    }

                    // Special handling for radio buttons
                    if (field.type === 'radio') {
                        const radioGroup = form.querySelectorAll(`input[name="${field.name}"]`);
                        const isRadioGroupChecked = Array.from(radioGroup).some(radio => radio.checked);

                        if (!isRadioGroupChecked) {
                            isValid = false;

                            // Add shake animation to the form group
                            if (formGroup) {
                                formGroup.classList.add('shake-animation');
                            }

                            // Add error message only once for the radio group
                            const existingError = formGroup.querySelector('.error-message');
                            if (!existingError) {
                                const errorMessage = document.createElement('p');
                                errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                                errorMessage.textContent = 'Please select an option';
                                formGroup.appendChild(errorMessage);
                            }
                        }
                    }
                    else if (!field.value.trim() || (field.type === 'checkbox' && !field.checked)) {
                        isValid = false;
                        field.classList.add('border-red-500');

                        // Add shake animation to the form group
                        if (formGroup) {
                            formGroup.classList.add('shake-animation');
                        }

                        // Add error message
                        const errorMessage = document.createElement('p');
                        errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                        errorMessage.textContent = 'This field is required';

                        // Insert after the input or its parent container
                        const container = field.closest('.relative') || field.closest('.flex') || field;
                        container.parentNode.insertBefore(errorMessage, container.nextSibling);
                    }
                    // Special validation for full name (no numbers allowed)
                    else if (field.id === 'fullName') {
                        // Check if the name contains any numbers
                        if (/[0-9]/.test(field.value.trim())) {
                            isValid = false;
                            field.classList.add('border-red-500');

                            // Add shake animation to the form group
                            if (formGroup) {
                                formGroup.classList.add('shake-animation');
                            }

                            // Add error message
                            const errorMessage = document.createElement('p');
                            errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                            errorMessage.textContent = 'Full name should not contain any numbers';

                            // Insert after the input or its parent container
                            const container = field.closest('.relative') || field;
                            container.parentNode.insertBefore(errorMessage, container.nextSibling);
                        }
                    }
                    // Special validation for phone number
                    else if (field.id === 'phone') {
                        // Check if phone number is valid (starts with a digit, no leading zero, and has 8-10 digits)
                        // This allows for a total of 12 digits including the +64 prefix
                        const phoneRegex = /^[1-9]\d{7,9}$/;
                        const phoneValue = field.value.trim();

                        // Check if the phone number is too long (more than 10 digits would make it over 12 with +64)
                        if (phoneValue.length > 10) {
                            isValid = false;
                            field.classList.add('border-red-500');

                            // Add shake animation to the form group
                            if (formGroup) {
                                formGroup.classList.add('shake-animation');
                            }

                            // Add error message
                            const errorMessage = document.createElement('p');
                            errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                            errorMessage.textContent = 'Phone number is too long. Maximum 10 digits allowed (12 with +64)';

                            // Insert after the input or its parent container
                            const container = field.closest('.flex') || field;
                            container.parentNode.insertBefore(errorMessage, container.nextSibling);
                        }
                        // Check if the phone number format is valid
                        else if (!phoneRegex.test(phoneValue)) {
                            isValid = false;
                            field.classList.add('border-red-500');

                            // Add shake animation to the form group
                            if (formGroup) {
                                formGroup.classList.add('shake-animation');
                            }

                            // Add error message
                            const errorMessage = document.createElement('p');
                            errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                            errorMessage.textContent = 'Please enter a valid NZ phone number without the leading 0';

                            // Insert after the input or its parent container
                            const container = field.closest('.flex') || field;
                            container.parentNode.insertBefore(errorMessage, container.nextSibling);
                        }
                    }
                    // Special validation for email
                    else if (field.id === 'email') {
                        // Comprehensive email validation regex
                        // This follows RFC 5322 standards and covers most valid email formats
                        const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

                        if (!emailRegex.test(field.value.trim())) {
                            isValid = false;
                            field.classList.add('border-red-500');

                            // Add shake animation to the form group
                            if (formGroup) {
                                formGroup.classList.add('shake-animation');
                            }

                            // Determine specific error message based on email format issues
                            let errorMsg = 'Please enter a valid email address';
                            const email = field.value.trim();

                            if (!email.includes('@')) {
                                errorMsg = 'Email address must contain an @ symbol';
                            } else if (email.indexOf('@') === 0) {
                                errorMsg = 'Email address cannot start with an @ symbol';
                            } else if (email.indexOf('@') === email.length - 1) {
                                errorMsg = 'Please include a domain after the @ symbol';
                            } else if (!email.substring(email.indexOf('@') + 1).includes('.')) {
                                errorMsg = 'Please include a valid domain with a dot (e.g., .com, .org)';
                            } else if (email.substring(email.lastIndexOf('.')).length < 2) {
                                errorMsg = 'Please include a valid domain extension (.com, .org, etc.)';
                            }

                            // Add error message
                            const errorMessage = document.createElement('p');
                            errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                            errorMessage.textContent = errorMsg;

                            // Insert after the input or its parent container
                            const container = field.closest('.relative') || field;
                            container.parentNode.insertBefore(errorMessage, container.nextSibling);
                        }
                    }
                });

                // Make learning style optional
                // const learningStyleSelected = document.querySelector('input[name="learningStyle"]:checked');
                // if (!learningStyleSelected) {
                //     isValid = false;
                //     const learningStyleSection = document.querySelector('.learning-style-option').closest('.mb-6');
                //
                //     // Add error message
                //     const errorMessage = document.createElement('p');
                //     errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-center');
                //     errorMessage.textContent = 'Please select a learning style';
                //     learningStyleSection.appendChild(errorMessage);
                //
                //     // Add shake animation
                //     learningStyleSection.classList.add('shake-animation');
                // }

                return isValid;
            }

            // Function to submit form to Google Form
            function submitToGoogleForm() {
                const form = document.querySelector('#registrationForm');

                // Get form values
                const fullName = document.getElementById('fullName').value;
                const email = document.getElementById('email').value;
                const phone = document.getElementById('phone').value;
                const motivation = document.getElementById('motivation').value;
                const comments = document.getElementById('comments').value;
                const whatsappConsent = document.getElementById('whatsappConsent').checked;
                const learningStyle = document.querySelector('input[name="learningStyle"]:checked')?.value || '';

                // Form submission in progress

                // Create a hidden form that will submit to Google Forms
                const hiddenForm = document.createElement('form');
                hiddenForm.method = 'POST';
                hiddenForm.action = 'https://docs.google.com/forms/d/e/1FAIpQLSf1npoyrUSWJgx_K2KhWUiFdKXgdI0e0lyWi0F8DMAXj5oh3Q/formResponse';
                hiddenForm.target = 'hidden-iframe'; // Target the iframe
                hiddenForm.style.display = 'none';

                // Create and append input fields
                const nameInput = document.createElement('input');
                nameInput.name = 'entry.496392723';
                nameInput.value = fullName;
                hiddenForm.appendChild(nameInput);

                const emailInput = document.createElement('input');
                emailInput.name = 'entry.2056515345';
                emailInput.value = email;
                hiddenForm.appendChild(emailInput);

                const phoneInput = document.createElement('input');
                phoneInput.name = 'entry.1435714806';
                // Add +64 prefix if not already included
                phoneInput.value = phone.startsWith('+64') ? phone : '+64' + phone;
                hiddenForm.appendChild(phoneInput);

                const motivationInput = document.createElement('input');
                motivationInput.name = 'entry.1984046647';
                motivationInput.value = motivation;
                hiddenForm.appendChild(motivationInput);

                const commentsInput = document.createElement('input');
                commentsInput.name = 'entry.1578709933';
                commentsInput.value = comments || '';
                hiddenForm.appendChild(commentsInput);

                // Add learning style as a separate field if it exists
                if (learningStyle) {
                    const learningStyleInput = document.createElement('input');
                    learningStyleInput.name = 'entry.1330502828';
                    learningStyleInput.value = learningStyle;
                    hiddenForm.appendChild(learningStyleInput);
                } else {
                    // Add default agreement value if no learning style is selected
                    const agreementInput = document.createElement('input');
                    agreementInput.name = 'entry.1330502828';
                    agreementInput.value = 'Agree';
                    hiddenForm.appendChild(agreementInput);
                }

                // Create hidden iframe to prevent page navigation
                let iframe = document.getElementById('hidden-iframe');
                if (!iframe) {
                    iframe = document.createElement('iframe');
                    iframe.name = 'hidden-iframe';
                    iframe.id = 'hidden-iframe';
                    iframe.style.display = 'none';
                    document.body.appendChild(iframe);
                }

                // Submit form to Google Forms

                // Append form to body, submit it, and remove it
                document.body.appendChild(hiddenForm);
                hiddenForm.submit();

                // Using only one submission method to prevent duplicates

                // Clean up after submission
                setTimeout(() => {
                    document.body.removeChild(hiddenForm);
                }, 500);
            }

            // Add interactive validation to form inputs
            formInputs.forEach(input => {
                // Special handling for radio buttons
                if (input.type === 'radio') {
                    input.addEventListener('change', function() {
                        // Remove error message when radio button is selected
                        const formGroup = this.closest('.form-group');
                        if (formGroup) {
                            const errorMessage = formGroup.querySelector('.error-message');
                            if (errorMessage) {
                                errorMessage.remove();
                            }
                            formGroup.classList.remove('shake-animation');
                        }
                    });
                }
                else if (input.type !== 'checkbox' && input.type !== 'radio') {
                    // Special handling for phone number to only allow digits
                    if (input.id === 'phone') {
                        // Handle input events for phone number
                        input.addEventListener('input', function(e) {
                            // Remove any non-digit characters
                            this.value = this.value.replace(/\D/g, '');

                            // Remove leading zero if entered
                            if (this.value.startsWith('0')) {
                                this.value = this.value.substring(1);
                            }

                            // Enforce maximum length (10 digits, which becomes 12 with +64)
                            if (this.value.length > 10) {
                                this.value = this.value.substring(0, 10);
                            }

                            const statusIcon = input.closest('.form-group').querySelector('.input-status-icon');
                            if (input.value.trim() !== '') {
                                // Remove error styling if present
                                input.classList.remove('border-red-500');
                                const errorMessage = input.closest('.form-group').querySelector('.error-message');
                                if (errorMessage) {
                                    errorMessage.remove();
                                }

                                // Hide status icon until validation on blur
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            } else {
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            }
                        });

                        // Add blur event for phone number validation
                        input.addEventListener('blur', function() {
                            if (this.value.trim() !== '') {
                                const phoneValue = this.value.trim();
                                const statusIcon = input.closest('.form-group').querySelector('.input-status-icon');

                                // Remove any existing error messages
                                const existingError = input.closest('.form-group').querySelector('.error-message');
                                if (existingError) {
                                    existingError.remove();
                                }

                                // Validate phone number
                                const phoneRegex = /^[1-9]\d{7,9}$/;

                                if (phoneRegex.test(phoneValue) && phoneValue.length <= 10) {
                                    // Valid phone number
                                    input.classList.remove('border-red-500');

                                    if (statusIcon) {
                                        statusIcon.classList.remove('hidden');
                                        statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                                        statusIcon.classList.remove('text-red-500');
                                        statusIcon.classList.add('text-green-500');
                                    }
                                } else {
                                    // Invalid phone number - error will be shown on form submission
                                    // We don't show an error message here to match the behavior with email
                                    if (statusIcon) {
                                        statusIcon.classList.add('hidden');
                                    }
                                }
                            }
                        });
                    }
                    // Special handling for email with validation only on blur
                    else if (input.id === 'email') {
                        // Only validate when user leaves the field
                        input.addEventListener('blur', function() {
                            if (this.value.trim() !== '') {
                                const email = this.value.trim();
                                const statusIcon = input.parentNode.querySelector('.input-status-icon');

                                // Remove any existing error messages
                                const existingError = input.closest('.form-group').querySelector('.error-message');
                                if (existingError) {
                                    existingError.remove();
                                }

                                // Validate email with a simple regex
                                const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

                                if (emailRegex.test(email)) {
                                    // Valid email
                                    input.classList.remove('border-red-500');

                                    if (statusIcon) {
                                        statusIcon.classList.remove('hidden');
                                        statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                                        statusIcon.classList.remove('text-red-500');
                                        statusIcon.classList.add('text-green-500');
                                    }
                                } else {
                                    // Invalid email
                                    input.classList.add('border-red-500');

                                    if (statusIcon) {
                                        statusIcon.classList.remove('hidden');
                                        statusIcon.innerHTML = '<i class="fas fa-exclamation-circle"></i>';
                                        statusIcon.classList.remove('text-green-500');
                                        statusIcon.classList.add('text-red-500');
                                    }

                                    // Add error message
                                    const errorMessage = document.createElement('p');
                                    errorMessage.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1', 'text-left');
                                    errorMessage.textContent = 'Please enter a valid email address';

                                    // Insert after the input container
                                    const container = input.closest('.relative');
                                    container.parentNode.insertBefore(errorMessage, container.nextSibling);
                                }
                            }
                        });

                        // Clear error styling when user starts typing again
                        input.addEventListener('input', function() {
                            const statusIcon = input.parentNode.querySelector('.input-status-icon');
                            if (input.value.trim() !== '') {
                                // Remove error styling if present
                                input.classList.remove('border-red-500');
                                const errorMessage = input.closest('.form-group').querySelector('.error-message');
                                if (errorMessage) {
                                    errorMessage.remove();
                                }

                                // Hide status icon until validation on blur
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            } else {
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            }
                        });
                    }
                    // Special handling for full name to prevent numbers
                    else if (input.id === 'fullName') {
                        input.addEventListener('input', function(e) {
                            // Remove any numeric characters
                            this.value = this.value.replace(/[0-9]/g, '');

                            const statusIcon = input.parentNode.querySelector('.input-status-icon');
                            if (input.value.trim() !== '') {
                                // Remove error styling if present
                                input.classList.remove('border-red-500');
                                const errorMessage = input.closest('.form-group').querySelector('.error-message');
                                if (errorMessage) {
                                    errorMessage.remove();
                                }

                                // Hide status icon until validation on blur
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            } else {
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            }
                        });

                        // Add blur event for full name validation
                        input.addEventListener('blur', function() {
                            if (this.value.trim() !== '') {
                                const fullName = this.value.trim();
                                const statusIcon = input.parentNode.querySelector('.input-status-icon');

                                // Remove any existing error messages
                                const existingError = input.closest('.form-group').querySelector('.error-message');
                                if (existingError) {
                                    existingError.remove();
                                }

                                // Validate full name (no numbers)
                                if (!/[0-9]/.test(fullName)) {
                                    // Valid full name
                                    input.classList.remove('border-red-500');

                                    if (statusIcon) {
                                        statusIcon.classList.remove('hidden');
                                        statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                                        statusIcon.classList.remove('text-red-500');
                                        statusIcon.classList.add('text-green-500');
                                    }
                                } else {
                                    // Invalid full name - error will be shown on form submission
                                    if (statusIcon) {
                                        statusIcon.classList.add('hidden');
                                    }
                                }
                            }
                        });
                    } else {
                        input.addEventListener('input', function() {
                            const statusIcon = input.parentNode.querySelector('.input-status-icon');
                            if (input.value.trim() !== '') {
                                if (statusIcon) {
                                    statusIcon.classList.remove('hidden');
                                }

                                // Remove error styling if present
                                input.classList.remove('border-red-500');
                                const errorMessage = input.parentNode.parentNode.querySelector('.error-message');
                                if (errorMessage) {
                                    errorMessage.remove();
                                }
                            } else {
                                if (statusIcon) {
                                    statusIcon.classList.add('hidden');
                                }
                            }
                        });
                    }

                    // Add focus effects
                    input.addEventListener('focus', function() {
                        const label = input.closest('.form-group').querySelector('label');
                        if (label) {
                            label.classList.add('text-primary');
                        }
                    });

                    input.addEventListener('blur', function() {
                        const label = input.closest('.form-group').querySelector('label');
                        if (label) {
                            label.classList.remove('text-primary');
                        }
                    });
                }
            });

            // Make learning style options interactive
            learningStyleOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active class from all options
                    learningStyleOptions.forEach(opt => {
                        opt.classList.remove('bg-white/20', 'border-primary');
                    });

                    // Add active class to clicked option
                    this.classList.add('bg-white/20', 'border-primary');

                    // Check the radio button
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;
                    }

                    // Remove error message if present
                    const errorMessage = this.closest('.mb-6').querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.remove();
                    }
                });
            });

            // Step 1 submit button - validate and move to step 2
            if (step1SubmitBtn) {
                step1SubmitBtn.addEventListener('click', function(e) {
                    e.preventDefault(); // Prevent form submission

                    // Add loading state to button
                    this.classList.add('opacity-75', 'pointer-events-none');
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';

                    // Validate form before moving to step 2
                    if (validateForm()) {
                        // Submit to Google Form
                        submitToGoogleForm();

                        // Show success animation before moving to step 2
                        setTimeout(() => {
                            this.innerHTML = '<i class="fas fa-check mr-2"></i> Moving to Step 2...';
                            this.classList.remove('opacity-75', 'pointer-events-none');
                            this.classList.add('bg-green-500');

                            // Move to step 2 after a short delay
                            setTimeout(() => {
                                showStep2();
                            }, 1000);
                        }, 1500);
                    } else {
                        // Restore button state
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.classList.remove('opacity-75', 'pointer-events-none');
                        }, 500);

                        // Scroll to the first error
                        const firstError = document.querySelector('.error-message');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                });
            }



            // WhatsApp button functionality
            if (joinWhatsAppButton) {
                // Check if user has already clicked WhatsApp button
                if (localStorage.getItem('whatsappClicked') === 'true') {
                    // Change button text to "Registration Complete"
                    joinWhatsAppButton.innerHTML = '<i class="fas fa-check text-xl mr-2"></i>REGISTRATION COMPLETE';
                    joinWhatsAppButton.classList.remove('bg-[#25D366]', 'hover:bg-[#25D366]/90');
                    joinWhatsAppButton.classList.add('bg-green-500', 'hover:bg-green-600');
                    joinWhatsAppButton.style.pointerEvents = 'none';
                }

                joinWhatsAppButton.addEventListener('click', function(e) {
                    // Only track the click if not already completed
                    if (localStorage.getItem('whatsappClicked') !== 'true') {
                        // Mark that user clicked WhatsApp button
                        localStorage.setItem('whatsappClicked', 'true');
                        localStorage.setItem('whatsappClickTime', Date.now().toString());

                        // Start periodic checking for mobile devices
                        startPeriodicCheck();
                        // Stop checking after 5 minutes to avoid unnecessary battery drain
                        setTimeout(stopPeriodicCheck, 300000);
                    }
                });
            }

            // Check for return from WhatsApp and show thank you modal
            function checkForWhatsAppReturn() {
                const whatsappClicked = localStorage.getItem('whatsappClicked');
                const clickTime = localStorage.getItem('whatsappClickTime');
                const modalShown = localStorage.getItem('thankYouModalShown');

                if (whatsappClicked === 'true' && clickTime && modalShown !== 'true') {
                    const timeSinceClick = Date.now() - parseInt(clickTime);
                    // If more than 3 seconds have passed since click, assume they returned
                    if (timeSinceClick > 3000) {
                        // Update button appearance
                        if (joinWhatsAppButton) {
                            joinWhatsAppButton.innerHTML = '<i class="fas fa-check text-xl mr-2"></i>REGISTRATION COMPLETE';
                            joinWhatsAppButton.classList.remove('bg-[#25D366]', 'hover:bg-[#25D366]/90');
                            joinWhatsAppButton.classList.add('bg-green-500', 'hover:bg-green-600');
                            joinWhatsAppButton.style.pointerEvents = 'none';
                        }

                        // Show thank you modal
                        setTimeout(() => {
                            showThankYouModal();
                            localStorage.setItem('thankYouModalShown', 'true');
                        }, 1000);
                    }
                }
            }

            // Multiple event listeners for better mobile detection

            // Listen for window focus (works on desktop)
            window.addEventListener('focus', function() {
                setTimeout(checkForWhatsAppReturn, 500);
            });

            // Listen for page visibility change (better for mobile)
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    setTimeout(checkForWhatsAppReturn, 500);
                }
            });

            // Listen for page show event (when returning from another app)
            window.addEventListener('pageshow', function(event) {
                setTimeout(checkForWhatsAppReturn, 500);
            });

            // Periodic check while page is visible (fallback for mobile)
            let checkInterval;
            function startPeriodicCheck() {
                checkInterval = setInterval(function() {
                    if (!document.hidden) {
                        checkForWhatsAppReturn();
                    }
                }, 2000); // Check every 2 seconds
            }

            function stopPeriodicCheck() {
                if (checkInterval) {
                    clearInterval(checkInterval);
                }
            }



            // Also check on page load
            setTimeout(checkForWhatsAppReturn, 1000);

            // Thank you modal event listeners
            if (closeThankYouModal) {
                closeThankYouModal.addEventListener('click', function() {
                    closeThankYouModalFunc();
                    // Reset form after modal closes
                    setTimeout(() => {
                        resetEntireForm();
                    }, 500);
                });
            }

            if (thankYouCloseBtn) {
                thankYouCloseBtn.addEventListener('click', function() {
                    closeThankYouModalFunc();
                    // Reset form and redirect to home after modal closes
                    setTimeout(() => {
                        resetEntireForm();
                        // Redirect to homepage
                        window.location.href = '/';
                    }, 500);
                });
            }

            // Close thank you modal when clicking outside
            if (thankYouModal) {
                thankYouModal.addEventListener('click', function(e) {
                    if (e.target === this || e.target.id === 'thankYouModalBackdrop') {
                        closeThankYouModalFunc();
                        // Reset form after modal closes
                        setTimeout(() => {
                            resetEntireForm();
                        }, 500);
                    }
                });
            }

            // Close thank you modal when pressing escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && thankYouModal && !thankYouModal.classList.contains('hidden')) {
                    closeThankYouModalFunc();
                    // Reset form after modal closes
                    setTimeout(() => {
                        resetEntireForm();
                    }, 500);
                }
            });

            // Multi-step form functionality is now complete
        });
    </script>

    <!-- Add animation styles -->
    <style>
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake-animation {
            animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
        }

        /* Smooth transitions for form elements */
        .form-group input, .form-group select, .form-group textarea {
            transition: all 0.3s ease;
        }

        /* Hover effects for learning style options */
        .learning-style-option:hover .w-12 {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }

        /* Focus styles */
        input:focus, select:focus, textarea:focus {
            box-shadow: 0 0 0 3px rgba(222, 174, 53, 0.3);
        }

        /* Multi-step Form Styles */
        .step-content {
            transition: all 0.3s ease;
        }

        .step-content.hidden {
            display: none;
        }

        /* Step indicator transitions */
        #step1-indicator, #step2-indicator {
            transition: all 0.3s ease;
        }

        #step-connector {
            transition: all 0.3s ease;
        }

        /* Thank You Modal Styles */
        #thankYouModal {
            transition: opacity 0.3s ease;
        }

        #thankYouModalContent {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        #thankYouModal.opacity-100 {
            opacity: 1 !important;
        }

        /* End of styles */
    </style>
</body>
</html>
